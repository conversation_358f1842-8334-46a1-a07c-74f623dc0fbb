#!/usr/bin/env python3
"""
测试GPU数据预加载输出信息的脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train_classify_SKD_TSTSAN_functions_SAMM2 import *

def test_gpu_preload_messages():
    """测试GPU预加载相关的输出信息"""
    
    print("="*80)
    print("🧪 测试GPU数据预加载输出信息")
    print("="*80)
    
    # 测试各种打印函数
    print_section("GPU数据预加载", Colors.BRIGHT_GREEN, "🚀")
    print_info("正在将所有数据预加载到GPU内存...", "📤")
    print_info("训练数据大小: 0.14 GB", "📊")
    print_info("测试数据大小: 0.02 GB", "📊")
    print_info("总数据大小: 0.17 GB", "📊")
    print_info("正在上传训练数据到GPU...", "⬆️")
    print_info("正在上传测试数据到GPU...", "⬆️")
    print_success("数据预加载到GPU完成！", "✅")
    print_info("GPU内存使用: GPU0: 0.2GB/79.3GB (0.2%)", "💾")
    
    print_success("✅ GPU数据预加载完成确认:", "🎯")
    print_info("  📦 训练数据集: 437 个样本已在GPU内存", "✅")
    print_info("  📦 测试数据集: 20 个样本已在GPU内存", "✅")
    print_info("  🚀 训练批次数: 14 个批次", "📊")
    print_info("  🚀 测试批次数: 1 个批次", "📊")
    print_info("  ⚡ 所有数据已驻留在GPU，训练时无需CPU-GPU传输！", "🚀")
    print_info("GPU预加载模式：数据传输将极大加速！", "⚡")
    
    print_banner("🚀 开始训练征程 🚀", Colors.BRIGHT_GREEN, "🎯")
    print_highlight("总共 1429 个epoch的精彩旅程即将开始！", "🌟")
    
    print_success("🚀 GPU加速模式已激活！", "⚡")
    print_info("  📍 所有训练和测试数据已预加载到GPU内存", "💾")
    print_info("  ⚡ 训练过程中将享受零延迟数据访问", "🚀")
    
    print_section("Epoch 1/1429", Colors.BRIGHT_CYAN, "📅")
    print_success("🚀 GPU数据访问确认: 数据直接从GPU内存读取，无CPU-GPU传输延迟！", "⚡")
    print_info("  📦 当前批次数据形状: torch.Size([32, 38, 48, 48]), 设备: cuda:0", "📊")
    
    print("\n" + "="*80)
    print("✅ GPU数据预加载输出信息测试完成！")
    print("="*80)

if __name__ == "__main__":
    test_gpu_preload_messages()
