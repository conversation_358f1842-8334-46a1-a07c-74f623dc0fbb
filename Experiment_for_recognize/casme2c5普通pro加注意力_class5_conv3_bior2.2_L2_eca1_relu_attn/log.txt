日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/casme2c5普通pro加注意力_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/casme2c5普通pro加注意力_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m🔥 启用三模态特征融合:[0m
  [1;96m多头注意力融合[0m 🧠
  [96m- 建立跨模态注意力关系[0m 🔗
  [96m- 动态关注重要特征[0m 🎯
  [1;96m- 注意力头数: 8[0m 🎯
[1;92m🛑 启用早停机制:[0m
  [1;96m智能训练优化[0m ⚡
  [96m- 监控指标: accuracy[0m 📊
  [96m- 耐心值: 300 epochs[0m ⏳
  [96m- 改善阈值: 0.001[0m 📈
  [96m- 自动保存最佳模型[0m 💾
  [96m- 防止过拟合，提高训练效率[0m 🎯

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;92m🚀 GPU数据预加载[0m
[1;92m------------[0m
[1;96m📤 正在将所有数据预加载到GPU内存...[0m
[1;96m📊 训练数据大小: 0.27 GB[0m
[1;96m📊 测试数据大小: 0.06 GB[0m
[1;96m📊 总数据大小: 0.32 GB[0m
[1;96m⬆️ 正在上传训练数据到GPU...[0m
[1;96m⬆️ 正在上传测试数据到GPU...[0m
[1;92m✅ 数据预加载到GPU完成！[0m
[1;96m💾 GPU内存使用: GPU0: 0.3GB/79.3GB (0.4%)[0m
[1;96m⚡ GPU预加载模式：数据传输将极大加速！[0m

[1m[1;93m🛑 早停配置[0m
[1;93m--------[0m
[1;96m📊 监控指标: accuracy[0m
[1;96m⏳ 耐心值: 300 epochs[0m
[1;96m📈 最小改善阈值: 0.001[0m
[1;96m🎯 模式: 最大化[0m

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 禁用[0m
[1;96m📈 Epoch损失统计: 禁用[0m
[1;96m📉 验证损失统计: 禁用[0m
[1;96m🚨 过拟合监控: 禁用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m❌ Epoch损失统计将显示: 否[0m
[1;96m❌ 验证损失统计将显示: 否[0m
[1;96m❌ 过拟合监控将显示: 否[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5639[0m (459/814)
   • 中间层1准确率: [96m0.5086[0m (414/814)
   • 中间层2准确率: [96m0.5737[0m (467/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
📊 [1mUF1:[0m [1;92m0.6452[0m | [1mUAR:[0m [1;92m0.6492[0m
🥇 [1m最佳UF1:[0m [1;93m0.0000[0m | [1m最佳UAR:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.6765 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.6452 | UAR: 0.6492 | 准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/casme2c5普通pro加注意力_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/casme2c5普通pro加注意力_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.6765 (最佳: 0.6765)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/casme2c5普通pro加注意力_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7543[0m (614/814)
   • 中间层1准确率: [96m0.7531[0m (613/814)
   • 中间层2准确率: [96m0.7764[0m (632/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6393[0m | [1mUAR:[0m [1;96m0.6270[0m
🥇 [1m最佳UF1:[0m [1;93m0.6452[0m | [1m最佳UAR:[0m [1;93m0.6492[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.7059)但UF1(0.6393↓)和UAR(0.6270↓)都下降，不保存[0m
[1;96m📈 🎯 accuracy改善: 0.7059 (最佳: 0.7059)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/casme2c5普通pro加注意力_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7998[0m (651/814)
   • 中间层1准确率: [96m0.7887[0m (642/814)
   • 中间层2准确率: [96m0.8034[0m (654/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7941[0m (27/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6255[0m | [1mUAR:[0m [1;92m0.6921[0m
🥇 [1m最佳UF1:[0m [1;93m0.6452[0m | [1m最佳UAR:[0m [1;93m0.6492[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.7941)但UF1下降(0.6255↓)，不保存[0m
[1;96m📈 🎯 accuracy改善: 0.7941 (最佳: 0.7941)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/casme2c5普通pro加注意力_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8526[0m (694/814)
   • 中间层1准确率: [96m0.8612[0m (701/814)
   • 中间层2准确率: [96m0.8403[0m (684/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5279[0m | [1mUAR:[0m [1;92m0.6587[0m
🥇 [1m最佳UF1:[0m [1;93m0.6452[0m | [1m最佳UAR:[0m [1;93m0.6492[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.7059)但UF1下降(0.5279↓)，不保存[0m
[1;96m🔄 ⏳ 无改善 (1/300) 剩余耐心: 299[0m

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8673[0m (706/814)
   • 中间层1准确率: [96m0.8747[0m (712/814)
   • 中间层2准确率: [96m0.8636[0m (703/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;92m0.6736[0m | [1mUAR:[0m [1;92m0.7143[0m
🥇 [1m最佳UF1:[0m [1;93m0.6452[0m | [1m最佳UAR:[0m [1;93m0.6492[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.7647 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.6736 | UAR: 0.7143 | 准确率: 0.7647[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/casme2c5普通pro加注意力_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/casme2c5普通pro加注意力_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth
[1;96m🔄 ⏳ 无改善 (2/300) 剩余耐心: 298[0m

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8857[0m (721/814)
   • 中间层1准确率: [96m0.8931[0m (727/814)
   • 中间层2准确率: [96m0.8771[0m (714/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6452[0m | [1mUAR:[0m [1;96m0.6810[0m
🥇 [1m最佳UF1:[0m [1;93m0.6736[0m | [1m最佳UAR:[0m [1;93m0.7143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (3/300) 剩余耐心: 297[0m

[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8735[0m (711/814)
   • 中间层1准确率: [96m0.8501[0m (692/814)
   • 中间层2准确率: [96m0.8722[0m (710/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5390[0m | [1mUAR:[0m [1;96m0.6190[0m
🥇 [1m最佳UF1:[0m [1;93m0.6736[0m | [1m最佳UAR:[0m [1;93m0.7143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (4/300) 剩余耐心: 296[0m

[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8968[0m (730/814)
   • 中间层1准确率: [96m0.8870[0m (722/814)
   • 中间层2准确率: [96m0.9103[0m (741/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6538[0m | [1mUAR:[0m [1;96m0.6889[0m
🥇 [1m最佳UF1:[0m [1;93m0.6736[0m | [1m最佳UAR:[0m [1;93m0.7143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (5/300) 剩余耐心: 295[0m

[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8698[0m (708/814)
   • 中间层1准确率: [96m0.8784[0m (715/814)
   • 中间层2准确率: [96m0.8624[0m (702/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.3392[0m | [1mUAR:[0m [1;96m0.3857[0m
🥇 [1m最佳UF1:[0m [1;93m0.6736[0m | [1m最佳UAR:[0m [1;93m0.7143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (6/300) 剩余耐心: 294[0m

[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9091[0m (740/814)
   • 中间层1准确率: [96m0.9128[0m (743/814)
   • 中间层2准确率: [96m0.9115[0m (742/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.4644[0m | [1mUAR:[0m [1;96m0.5000[0m
🥇 [1m最佳UF1:[0m [1;93m0.6736[0m | [1m最佳UAR:[0m [1;93m0.7143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (7/300) 剩余耐心: 293[0m

[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8968[0m (730/814)
   • 中间层1准确率: [96m0.9189[0m (748/814)
   • 中间层2准确率: [96m0.9079[0m (739/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6000[0m | [1mUAR:[0m [1;96m0.6222[0m
🥇 [1m最佳UF1:[0m [1;93m0.6736[0m | [1m最佳UAR:[0m [1;93m0.7143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (8/300) 剩余耐心: 292[0m

[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9484[0m (772/814)
   • 中间层1准确率: [96m0.9447[0m (769/814)
   • 中间层2准确率: [96m0.9570[0m (779/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5792[0m | [1mUAR:[0m [1;96m0.6000[0m
🥇 [1m最佳UF1:[0m [1;93m0.6736[0m | [1m最佳UAR:[0m [1;93m0.7143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (9/300) 剩余耐心: 291[0m

[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9545[0m (777/814)
   • 中间层1准确率: [96m0.9533[0m (776/814)
   • 中间层2准确率: [96m0.9533[0m (776/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5452[0m | [1mUAR:[0m [1;96m0.6302[0m
🥇 [1m最佳UF1:[0m [1;93m0.6736[0m | [1m最佳UAR:[0m [1;93m0.7143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (10/300) 剩余耐心: 290[0m

[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9693[0m (789/814)
   • 中间层1准确率: [96m0.9767[0m (795/814)
   • 中间层2准确率: [96m0.9705[0m (790/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6656[0m | [1mUAR:[0m [1;92m0.7571[0m
🥇 [1m最佳UF1:[0m [1;93m0.6736[0m | [1m最佳UAR:[0m [1;93m0.7143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (11/300) 剩余耐心: 289[0m

[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9767[0m (795/814)
   • 中间层1准确率: [96m0.9619[0m (783/814)
   • 中间层2准确率: [96m0.9742[0m (793/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;92m0.7626[0m | [1mUAR:[0m [1;92m0.7825[0m
🥇 [1m最佳UF1:[0m [1;93m0.6736[0m | [1m最佳UAR:[0m [1;93m0.7143[0m
============================================================

[1;92m💾 保存最佳模型，相同准确率下UF1和UAR都提升: UF1(0.7626↑) UAR(0.7825↑)[0m
[1;96m📊 UF1: 0.7626 | UAR: 0.7825 | 准确率: 0.7647[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/casme2c5普通pro加注意力_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/casme2c5普通pro加注意力_class5_conv3_bior2.2_L2_eca1_relu_attn/sub17/sub17.pth
[1;96m🔄 ⏳ 无改善 (12/300) 剩余耐心: 288[0m

[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9509[0m (774/814)
   • 中间层1准确率: [96m0.9226[0m (751/814)
   • 中间层2准确率: [96m0.9509[0m (774/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6764[0m | [1mUAR:[0m [1;96m0.7492[0m
🥇 [1m最佳UF1:[0m [1;93m0.7626[0m | [1m最佳UAR:[0m [1;93m0.7825[0m
============================================================

[1;96m🔄 ⏳ 无改善 (13/300) 剩余耐心: 287[0m

[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9263[0m (754/814)
   • 中间层1准确率: [96m0.9054[0m (737/814)
   • 中间层2准确率: [96m0.9398[0m (765/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5988[0m | [1mUAR:[0m [1;96m0.7492[0m
🥇 [1m最佳UF1:[0m [1;93m0.7626[0m | [1m最佳UAR:[0m [1;93m0.7825[0m
============================================================

[1;96m🔄 ⏳ 无改善 (14/300) 剩余耐心: 286[0m

[1m[1;96m📅 Epoch 18/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9226[0m (751/814)
   • 中间层1准确率: [96m0.9324[0m (759/814)
   • 中间层2准确率: [96m0.9177[0m (747/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5972[0m | [1mUAR:[0m [1;96m0.6000[0m
🥇 [1m最佳UF1:[0m [1;93m0.7626[0m | [1m最佳UAR:[0m [1;93m0.7825[0m
============================================================

[1;96m🔄 ⏳ 无改善 (15/300) 剩余耐心: 285[0m

[1m[1;96m📅 Epoch 19/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9730[0m (792/814)
   • 中间层1准确率: [96m0.9521[0m (775/814)
   • 中间层2准确率: [96m0.9656[0m (786/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6225[0m | [1mUAR:[0m [1;96m0.6825[0m
🥇 [1m最佳UF1:[0m [1;93m0.7626[0m | [1m最佳UAR:[0m [1;93m0.7825[0m
============================================================

[1;96m🔄 ⏳ 无改善 (16/300) 剩余耐心: 284[0m

[1m[1;96m📅 Epoch 20/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9828[0m (800/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5236[0m | [1mUAR:[0m [1;96m0.5873[0m
🥇 [1m最佳UF1:[0m [1;93m0.7626[0m | [1m最佳UAR:[0m [1;93m0.7825[0m
============================================================

[1;96m🔄 ⏳ 无改善 (17/300) 剩余耐心: 283[0m

[1m[1;96m📅 Epoch 21/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6318[0m | [1mUAR:[0m [1;96m0.6873[0m
🥇 [1m最佳UF1:[0m [1;93m0.7626[0m | [1m最佳UAR:[0m [1;93m0.7825[0m
============================================================

[1;96m🔄 ⏳ 无改善 (18/300) 剩余耐心: 282[0m

[1m[1;96m📅 Epoch 22/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 22 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 22 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6397[0m | [1mUAR:[0m [1;96m0.6889[0m
🥇 [1m最佳UF1:[0m [1;93m0.7626[0m | [1m最佳UAR:[0m [1;93m0.7825[0m
============================================================

[1;96m🔄 ⏳ 无改善 (19/300) 剩余耐心: 281[0m

[1m[1;96m📅 Epoch 23/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 23 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 23 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5339[0m | [1mUAR:[0m [1;96m0.6016[0m
🥇 [1m最佳UF1:[0m [1;93m0.7626[0m | [1m最佳UAR:[0m [1;93m0.7825[0m
============================================================

[1;96m🔄 ⏳ 无改善 (20/300) 剩余耐心: 280[0m

[1m[1;96m📅 Epoch 24/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 24 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 24 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5603[0m | [1mUAR:[0m [1;96m0.5921[0m
🥇 [1m最佳UF1:[0m [1;93m0.7626[0m | [1m最佳UAR:[0m [1;93m0.7825[0m
============================================================

[1;96m🔄 ⏳ 无改善 (21/300) 剩余耐心: 279[0m

[1m[1;96m📅 Epoch 25/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 25 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9681[0m (788/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m0.9693[0m (789/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 25 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.4783[0m | [1mUAR:[0m [1;96m0.5270[0m
🥇 [1m最佳UF1:[0m [1;93m0.7626[0m | [1m最佳UAR:[0m [1;93m0.7825[0m
============================================================

[1;96m🔄 ⏳ 无改善 (22/300) 剩余耐心: 278[0m

[1m[1;96m📅 Epoch 26/770[0m
[1;96m----------------[0m
