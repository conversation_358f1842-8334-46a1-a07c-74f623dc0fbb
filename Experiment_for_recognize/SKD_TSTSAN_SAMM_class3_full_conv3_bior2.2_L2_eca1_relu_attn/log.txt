日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (SAMM 3分类)[0m
[1;96m🎯 类别权重: ['0.3309', '0.0956', '0.5735'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: 011
========================================
组别: 大样本组(>8个测试样本)
正在加载训练数据...
正在加载测试数据...

011测试标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0]
创建受试者目录: 011
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn/011/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn/011/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m🔥 启用三模态特征融合:[0m
  [1;96m多头注意力融合[0m 🧠
  [96m- 建立跨模态注意力关系[0m 🔗
  [96m- 动态关注重要特征[0m 🎯
  [1;96m- 注意力头数: 8[0m 🎯
[1;92m🛑 启用早停机制:[0m
  [1;96m智能训练优化[0m ⚡
  [96m- 监控指标: accuracy[0m 📊
  [96m- 耐心值: 300 epochs[0m ⏳
  [96m- 改善阈值: 0.001[0m 📈
  [96m- 自动保存最佳模型[0m 💾
  [96m- 防止过拟合，提高训练效率[0m 🎯

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;92m🚀 GPU数据预加载[0m
[1;92m------------[0m
[1;96m📤 正在将所有数据预加载到GPU内存...[0m
[1;96m📊 训练数据大小: 0.14 GB[0m
[1;96m📊 测试数据大小: 0.02 GB[0m
[1;96m📊 总数据大小: 0.17 GB[0m
[1;96m⬆️ 正在上传训练数据到GPU...[0m
[1;96m⬆️ 正在上传测试数据到GPU...[0m
[1;92m✅ 数据预加载到GPU完成！[0m
[1;96m💾 GPU内存使用: GPU0: 0.2GB/79.3GB (0.2%)[0m
[1;96m⚡ GPU预加载模式：数据传输将极大加速！[0m

[1m[1;93m🛑 早停配置[0m
[1;93m--------[0m
[1;96m📊 监控指标: accuracy[0m
[1;96m⏳ 耐心值: 300 epochs[0m
[1;96m📈 最小改善阈值: 0.001[0m
[1;96m🎯 模式: 最大化[0m

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 禁用[0m
[1;96m📈 Epoch损失统计: 禁用[0m
[1;96m📉 验证损失统计: 禁用[0m
[1;96m🚨 过拟合监控: 禁用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m❌ Epoch损失统计将显示: 否[0m
[1;96m❌ 验证损失统计将显示: 否[0m
[1;96m❌ 过拟合监控将显示: 否[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 1429 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/1429[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5515[0m (241/437)
   • 中间层1准确率: [96m0.5172[0m (226/437)
   • 中间层2准确率: [96m0.5332[0m (233/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.3500[0m (7/20)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
📊 [1mUF1:[0m [1;92m0.2593[0m | [1mUAR:[0m [1;92m0.5000[0m
🥇 [1m最佳UF1:[0m [1;93m0.0000[0m | [1m最佳UAR:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.3500 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.2593 | UAR: 0.5000 | 准确率: 0.3500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth
[1;96m📈 🎯 accuracy改善: 0.3500 (最佳: 0.3500)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn/011/best_model_011.pth[0m

[1m[1;96m📅 Epoch 2/1429[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8192[0m (358/437)
   • 中间层1准确率: [96m0.7300[0m (319/437)
   • 中间层2准确率: [96m0.7666[0m (335/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6000[0m (12/20)
🏆 [1m最佳准确率:[0m [1;93m0.3500[0m
📊 [1mUF1:[0m [1;92m0.6913[0m | [1mUAR:[0m [1;92m0.5604[0m
🥇 [1m最佳UF1:[0m [1;93m0.2593[0m | [1m最佳UAR:[0m [1;93m0.5000[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.6000 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.6913 | UAR: 0.5604 | 准确率: 0.6000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth
[1;96m📈 🎯 accuracy改善: 0.6000 (最佳: 0.6000)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/SKD_TSTSAN_SAMM_class3_full_conv3_bior2.2_L2_eca1_relu_attn/011/best_model_011.pth[0m

[1m[1;96m📅 Epoch 3/1429[0m
[1;96m----------------[0m
