日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m✅ 启用标准LEGM模块:[0m
  [96m- 在conv1后增强初期特征提取[0m 🎯
  [96m- 专注于低层纹理和边缘特征捕获[0m 🔍
  [96m- 参数增加约6.5%，性能提升显著[0m 📈
  [1;95m- 推荐选择，性能与效率平衡[0m ⭐

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5332[0m (434/814)
   • 中间层1准确率: [96m0.5000[0m (407/814)
   • 中间层2准确率: [96m0.5184[0m (422/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6176[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7236[0m (589/814)
   • 中间层1准确率: [96m0.6990[0m (569/814)
   • 中间层2准确率: [96m0.7199[0m (586/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6471[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8317[0m (677/814)
   • 中间层1准确率: [96m0.7850[0m (639/814)
   • 中间层2准确率: [96m0.8366[0m (681/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
============================================================


[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8636[0m (703/814)
   • 中间层1准确率: [96m0.8305[0m (676/814)
   • 中间层2准确率: [96m0.8747[0m (712/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9165[0m (746/814)
   • 中间层1准确率: [96m0.8735[0m (711/814)
   • 中间层2准确率: [96m0.9189[0m (748/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9496[0m (773/814)
   • 中间层1准确率: [96m0.9189[0m (748/814)
   • 中间层2准确率: [96m0.9533[0m (776/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9435[0m (768/814)
   • 中间层1准确率: [96m0.9251[0m (753/814)
   • 中间层2准确率: [96m0.9435[0m (768/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.7059[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth

[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9717[0m (791/814)
   • 中间层1准确率: [96m0.9447[0m (769/814)
   • 中间层2准确率: [96m0.9693[0m (789/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.7353[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth

[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9631[0m (784/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9803[0m (798/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9767[0m (795/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9779[0m (796/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m0.9767[0m (795/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9803[0m (798/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2941[0m (10/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================


[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7941[0m (27/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.7941[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth

[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 18/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 19/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 20/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2941[0m (10/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 21/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9705[0m (790/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m0.9693[0m (789/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 22/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 22 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 22 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 23/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 23 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 23 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 24/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 24 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9779[0m (796/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m0.9828[0m (800/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 24 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 25/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 25 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 25 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 26/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 26 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 26 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 27/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 27 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 27 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 28/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 28 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 28 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 29/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 29 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 29 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.8235[0m (28/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.8235[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth

[1m[1;96m📅 Epoch 30/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 30 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9767[0m (795/814)
   • 中间层1准确率: [96m0.9803[0m (798/814)
   • 中间层2准确率: [96m0.9840[0m (801/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 30 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 31/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 31 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 31 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 32/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 32 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 32 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 33/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 33 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 33 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 34/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 34 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 34 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 35/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 35 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 35 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 36/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 36 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 36 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 37/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 37 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 37 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 38/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 38 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 38 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 39/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 39 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 39 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 40/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 40 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 40 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 41/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 41 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 41 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 42/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 42 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 42 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 43/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 43 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 43 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 44/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 44 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 44 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 45/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 45 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9840[0m (801/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 45 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 46/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 46 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 46 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 47/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 47 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 47 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 48/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 48 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 48 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 49/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 49 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 49 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 50/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 50 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 50 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 51/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 51 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 51 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 52/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 52 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 52 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 53/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 53 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 53 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 54/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 54 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 54 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 55/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 55 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9803[0m (798/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 55 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 56/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 56 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9816[0m (799/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 56 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 57/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 57 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 57 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2647[0m (9/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 58/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 58 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 58 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 59/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 59 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 59 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 60/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 60 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 60 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 61/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 61 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 61 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 62/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 62 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 62 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 63/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 63 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 63 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 64/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 64 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 64 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 65/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 65 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 65 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 66/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 66 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 66 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 67/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 67 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 67 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 68/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 68 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 68 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8235[0m (28/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.8235[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca2_dcbf_ltsm/sub17/sub17.pth

[1m[1;96m📅 Epoch 69/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 69 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 69 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 70/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 70 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 70 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 71/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 71 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 71 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 72/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 72 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 72 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 73/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 73 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 73 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 74/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 74 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 74 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 75/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 75 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 75 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 76/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 76 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 76 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 77/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 77 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 77 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 78/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 78 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 78 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 79/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 79 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9791[0m (797/814)
   • 中间层1准确率: [96m0.9742[0m (793/814)
   • 中间层2准确率: [96m0.9767[0m (795/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 79 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 80/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 80 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 80 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 81/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 81 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 81 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 82/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 82 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 82 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 83/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 83 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 83 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 84/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 84 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 84 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 85/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 85 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 85 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 86/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 86 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 86 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 87/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 87 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 87 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 88/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 88 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 88 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 89/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 89 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 89 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 90/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 90 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 90 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 91/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 91 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 91 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 92/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 92 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 92 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 93/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 93 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 93 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 94/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 94 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 94 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 95/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 95 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 95 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 96/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 96 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 96 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 97/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 97 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 97 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 98/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 98 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 98 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 99/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 99 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 99 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 100/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 100 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 100 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 101/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 101 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 101 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 102/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 102 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 102 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 103/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 103 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 103 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 104/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 104 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 104 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 105/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 105 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 105 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 106/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 106 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 106 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 107/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 107 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 107 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 108/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 108 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9791[0m (797/814)
   • 中间层2准确率: [96m0.9840[0m (801/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 108 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 109/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 109 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 109 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 110/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 110 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 110 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 111/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 111 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 111 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 112/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 112 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 112 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 113/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 113 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 113 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 114/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 114 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 114 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 115/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 115 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 115 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 116/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 116 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 116 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 117/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 117 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 117 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 118/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 118 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 118 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 119/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 119 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 119 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 120/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 120 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 120 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 121/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 121 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 121 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 122/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 122 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 122 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 123/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 123 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9865[0m (803/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 123 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 124/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 124 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 124 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 125/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 125 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 125 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 126/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 126 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 126 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 127/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 127 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 127 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 128/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 128 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 128 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 129/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 129 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 129 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 130/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 130 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 130 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 131/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 131 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 131 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 132/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 132 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 132 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 133/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 133 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 133 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 134/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 134 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9816[0m (799/814)
   • 中间层1准确率: [96m0.9767[0m (795/814)
   • 中间层2准确率: [96m0.9767[0m (795/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 134 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 135/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 135 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 135 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 136/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 136 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 136 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 137/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 137 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 137 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 138/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 138 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 138 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 139/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 139 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 139 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 140/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 140 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 140 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 141/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 141 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 141 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 142/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 142 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 142 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 143/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 143 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 143 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 144/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 144 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 144 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 145/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 145 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 145 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 146/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 146 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 146 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 147/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 147 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 147 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 148/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 148 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 148 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 149/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 149 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9816[0m (799/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m0.9828[0m (800/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 149 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 150/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 150 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 150 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 151/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 151 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 151 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 152/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 152 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 152 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 153/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 153 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 153 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 154/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 154 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 154 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 155/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 155 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 155 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 156/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 156 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 156 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 157/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 157 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 157 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 158/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 158 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 158 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 159/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 159 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 159 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 160/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 160 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 160 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 161/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 161 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 161 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 162/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 162 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 162 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 163/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 163 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 163 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 164/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 164 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 164 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 165/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 165 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 165 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 166/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 166 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 166 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 167/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 167 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 167 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 168/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 168 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 168 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 169/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 169 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 169 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 170/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 170 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 170 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 171/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 171 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 171 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 172/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 172 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 172 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 173/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 173 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 173 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 174/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 174 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 174 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 175/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 175 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 175 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 176/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 176 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 176 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 177/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 177 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 177 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 178/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 178 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9767[0m (795/814)
   • 中间层1准确率: [96m0.9767[0m (795/814)
   • 中间层2准确率: [96m0.9717[0m (791/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 178 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 179/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 179 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9803[0m (798/814)
   • 中间层1准确率: [96m0.9742[0m (793/814)
   • 中间层2准确率: [96m0.9828[0m (800/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 179 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 180/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 180 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 180 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 181/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 181 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 181 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 182/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 182 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 182 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 183/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 183 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 183 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 184/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 184 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 184 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 185/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 185 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 185 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 186/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 186 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 186 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 187/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 187 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 187 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 188/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 188 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 188 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 189/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 189 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 189 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 190/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 190 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 190 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 191/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 191 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 191 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 192/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 192 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 192 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 193/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 193 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 193 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 194/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 194 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 194 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 195/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 195 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 195 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 196/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 196 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 196 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 197/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 197 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9730[0m (792/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9754[0m (794/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 197 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 198/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 198 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 198 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 199/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 199 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 199 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 200/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 200 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9656[0m (786/814)
   • 中间层1准确率: [96m0.9717[0m (791/814)
   • 中间层2准确率: [96m0.9656[0m (786/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 200 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 201/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 201 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 201 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 202/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 202 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 202 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 203/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 203 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 203 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 204/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 204 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 204 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 205/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 205 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 205 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 206/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 206 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 206 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 207/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 207 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 207 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 208/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 208 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 208 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 209/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 209 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 209 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 210/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 210 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 210 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 211/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 211 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 211 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 212/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 212 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 212 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 213/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 213 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 213 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 214/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 214 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 214 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 215/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 215 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 215 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 216/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 216 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 216 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 217/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 217 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 217 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 218/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 218 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 218 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 219/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 219 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 219 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 220/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 220 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 220 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 221/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 221 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 221 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 222/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 222 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 222 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 223/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 223 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 223 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 224/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 224 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 224 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 225/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 225 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 225 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 226/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 226 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 226 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 227/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 227 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 227 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 228/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 228 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 228 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 229/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 229 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 229 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 230/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 230 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 230 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 231/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 231 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 231 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 232/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 232 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 232 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 233/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 233 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 233 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 234/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 234 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 234 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 235/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 235 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 235 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 236/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 236 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 236 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2353[0m (8/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 237/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 237 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 237 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 238/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 238 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 238 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 239/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 239 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 239 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 240/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 240 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 240 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 241/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 241 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 241 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 242/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 242 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 242 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 243/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 243 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 243 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 244/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 244 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 244 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 245/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 245 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 245 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 246/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 246 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 246 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 247/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 247 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 247 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 248/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 248 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 248 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 249/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 249 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 249 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 250/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 250 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 250 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 251/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 251 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 251 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 252/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 252 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 252 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 253/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 253 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 253 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 254/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 254 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 254 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 255/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 255 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 255 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 256/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 256 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 256 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 257/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 257 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 257 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2059[0m (7/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 258/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 258 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 258 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 259/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 259 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 259 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 260/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 260 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 260 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 261/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 261 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 261 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 262/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 262 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 262 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 263/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 263 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 263 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 264/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 264 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 264 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 265/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 265 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 265 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 266/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 266 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 266 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 267/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 267 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 267 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 268/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 268 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 268 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 269/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 269 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 269 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 270/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 270 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 270 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7941[0m (27/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 271/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 271 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9767[0m (795/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9754[0m (794/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 271 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 272/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 272 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 272 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 273/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 273 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 273 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 274/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 274 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 274 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 275/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 275 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 275 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 276/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 276 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 276 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 277/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 277 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 277 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 278/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 278 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 278 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 279/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 279 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 279 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 280/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 280 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 280 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 281/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 281 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 281 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 282/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 282 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 282 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 283/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 283 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 283 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 284/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 284 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 284 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 285/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 285 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 285 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 286/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 286 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9828[0m (800/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 286 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 287/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 287 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9828[0m (800/814)
   • 中间层2准确率: [96m0.9828[0m (800/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 287 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 288/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 288 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 288 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 289/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 289 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 289 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 290/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 290 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 290 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 291/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 291 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 291 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 292/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 292 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 292 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 293/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 293 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 293 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 294/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 294 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 294 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 295/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 295 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 295 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 296/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 296 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 296 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 297/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 297 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 297 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 298/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 298 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 298 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 299/770[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 299 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 299 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.8235[0m
============================================================


[1m[1;96m📅 Epoch 300/770[0m
[1;96m-----------------[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

正在关闭日志系统...
