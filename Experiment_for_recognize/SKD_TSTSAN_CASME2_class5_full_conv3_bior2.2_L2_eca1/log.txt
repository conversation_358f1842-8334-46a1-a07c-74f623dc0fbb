日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m✅ 启用标准LEGM模块:[0m
  [96m- 在conv1后增强初期特征提取[0m 🎯
  [96m- 专注于低层纹理和边缘特征捕获[0m 🔍
  [96m- 参数增加约6.5%，性能提升显著[0m 📈
  [1;95m- 推荐选择，性能与效率平衡[0m ⭐

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.6474[0m (527/814)
   • 中间层1准确率: [96m0.6474[0m (527/814)
   • 中间层2准确率: [96m0.6216[0m (506/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8477[0m (690/814)
   • 中间层1准确率: [96m0.8378[0m (682/814)
   • 中间层2准确率: [96m0.8636[0m (703/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9545[0m (777/814)
   • 中间层1准确率: [96m0.9459[0m (770/814)
   • 中间层2准确率: [96m0.9619[0m (783/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9889[0m (805/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9828[0m (800/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.7059[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
============================================================


[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9754[0m (794/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m0.9754[0m (794/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
============================================================


[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
============================================================


[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.7647[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9926[0m (808/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9447[0m (769/814)
   • 中间层1准确率: [96m0.9570[0m (779/814)
   • 中间层2准确率: [96m0.9361[0m (762/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9656[0m (786/814)
   • 中间层1准确率: [96m0.9791[0m (797/814)
   • 中间层2准确率: [96m0.9656[0m (786/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9853[0m (802/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 18/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 19/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 20/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 21/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 22/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 22 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 22 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 23/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 23 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 23 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 24/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 24 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9951[0m (810/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 24 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 25/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 25 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 25 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 26/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 26 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9914[0m (807/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 26 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 27/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 27 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9914[0m (807/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 27 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 28/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 28 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9828[0m (800/814)
   • 中间层2准确率: [96m0.9840[0m (801/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 28 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 29/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 29 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9840[0m (801/814)
   • 中间层2准确率: [96m0.9816[0m (799/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 29 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 30/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 30 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9877[0m (804/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 30 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 31/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 31 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 31 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 32/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 32 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 32 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 33/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 33 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 33 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 34/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 34 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 34 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 35/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 35 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 35 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 36/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 36 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 36 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 37/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 37 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9816[0m (799/814)
   • 中间层1准确率: [96m0.9889[0m (805/814)
   • 中间层2准确率: [96m0.9840[0m (801/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 37 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 38/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 38 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9902[0m (806/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 38 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 39/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 39 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 39 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 40/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 40 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 40 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 41/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 41 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 41 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 42/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 42 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 42 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 43/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 43 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9914[0m (807/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 43 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 44/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 44 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9939[0m (809/814)
   • 中间层1准确率: [96m0.9963[0m (811/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 44 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 45/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 45 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 45 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 46/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 46 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9926[0m (808/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 46 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 47/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 47 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 47 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 48/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 48 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 48 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
============================================================


[1m[1;96m📅 Epoch 49/770[0m
[1;96m----------------[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
TensorBoard日志目录已存在: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m✅ 启用标准LEGM模块:[0m
  [96m- 在conv1后增强初期特征提取[0m 🎯
  [96m- 专注于低层纹理和边缘特征捕获[0m 🔍
  [96m- 参数增加约6.5%，性能提升显著[0m 📈
  [1;95m- 推荐选择，性能与效率平衡[0m ⭐

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5283[0m (430/814)
   • 中间层1准确率: [96m0.5000[0m (407/814)
   • 中间层2准确率: [96m0.5209[0m (424/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7236[0m (589/814)
   • 中间层1准确率: [96m0.6855[0m (558/814)
   • 中间层2准确率: [96m0.7162[0m (583/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8415[0m (685/814)
   • 中间层1准确率: [96m0.7973[0m (649/814)
   • 中间层2准确率: [96m0.8415[0m (685/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

正在关闭日志系统...
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
TensorBoard日志目录已存在: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m✅ 启用标准LEGM模块:[0m
  [96m- 在conv1后增强初期特征提取[0m 🎯
  [96m- 专注于低层纹理和边缘特征捕获[0m 🔍
  [96m- 参数增加约6.5%，性能提升显著[0m 📈
  [1;95m- 推荐选择，性能与效率平衡[0m ⭐

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.3329[0m (271/814)
   • 中间层1准确率: [96m0.3194[0m (260/814)
   • 中间层2准确率: [96m0.3022[0m (246/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.1176[0m (4/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.1176[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
正在加载测试数据...
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
TensorBoard日志目录已存在: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m✅ 启用标准LEGM模块:[0m
  [96m- 在conv1后增强初期特征提取[0m 🎯
  [96m- 专注于低层纹理和边缘特征捕获[0m 🔍
  [96m- 参数增加约6.5%，性能提升显著[0m 📈
  [1;95m- 推荐选择，性能与效率平衡[0m ⭐

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.4705[0m (383/814)
   • 中间层1准确率: [96m0.4472[0m (364/814)
   • 中间层2准确率: [96m0.4558[0m (371/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.1176[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.3529[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5614[0m (457/814)
   • 中间层1准确率: [96m0.4926[0m (401/814)
   • 中间层2准确率: [96m0.5393[0m (439/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.3529[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.3529[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.6314[0m (514/814)
   • 中间层1准确率: [96m0.5983[0m (487/814)
   • 中间层2准确率: [96m0.6634[0m (540/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.3529[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.5588[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5319[0m (433/814)
   • 中间层1准确率: [96m0.4988[0m (406/814)
   • 中间层2准确率: [96m0.5184[0m (422/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.6769[0m (551/814)
   • 中间层1准确率: [96m0.6658[0m (542/814)
   • 中间层2准确率: [96m0.7211[0m (587/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.5588[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7310[0m (595/814)
   • 中间层1准确率: [96m0.6904[0m (562/814)
   • 中间层2准确率: [96m0.7150[0m (582/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7224[0m (588/814)
   • 中间层1准确率: [96m0.6904[0m (562/814)
   • 中间层2准确率: [96m0.7457[0m (607/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.7353[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8268[0m (673/814)
   • 中间层1准确率: [96m0.8010[0m (652/814)
   • 中间层2准确率: [96m0.8403[0m (684/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7690[0m (626/814)
   • 中间层1准确率: [96m0.7322[0m (596/814)
   • 中间层2准确率: [96m0.7912[0m (644/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7941[0m (27/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.7941[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth

[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8870[0m (722/814)
   • 中间层1准确率: [96m0.8526[0m (694/814)
   • 中间层2准确率: [96m0.8845[0m (720/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7973[0m (649/814)
   • 中间层1准确率: [96m0.7518[0m (612/814)
   • 中间层2准确率: [96m0.8120[0m (661/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9152[0m (745/814)
   • 中间层1准确率: [96m0.8882[0m (723/814)
   • 中间层2准确率: [96m0.9128[0m (743/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8415[0m (685/814)
   • 中间层1准确率: [96m0.7666[0m (624/814)
   • 中间层2准确率: [96m0.8550[0m (696/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9447[0m (769/814)
   • 中间层1准确率: [96m0.9115[0m (742/814)
   • 中间层2准确率: [96m0.9509[0m (774/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8735[0m (711/814)
   • 中间层1准确率: [96m0.7862[0m (640/814)
   • 中间层2准确率: [96m0.8747[0m (712/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9275[0m (755/814)
   • 中间层1准确率: [96m0.9177[0m (747/814)
   • 中间层2准确率: [96m0.9300[0m (757/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8943[0m (728/814)
   • 中间层1准确率: [96m0.7948[0m (647/814)
   • 中间层2准确率: [96m0.8956[0m (729/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2941[0m (10/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9165[0m (746/814)
   • 中间层1准确率: [96m0.8231[0m (670/814)
   • 中间层2准确率: [96m0.9066[0m (738/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9644[0m (785/814)
   • 中间层1准确率: [96m0.9398[0m (765/814)
   • 中间层2准确率: [96m0.9595[0m (781/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9853[0m (802/814)
   • 中间层1准确率: [96m0.9644[0m (785/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9251[0m (753/814)
   • 中间层1准确率: [96m0.8329[0m (678/814)
   • 中间层2准确率: [96m0.9152[0m (745/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9963[0m (811/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9423[0m (767/814)
   • 中间层1准确率: [96m0.8489[0m (691/814)
   • 中间层2准确率: [96m0.9410[0m (766/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9742[0m (793/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9730[0m (792/814)
   • 中间层1准确率: [96m0.8919[0m (726/814)
   • 中间层2准确率: [96m0.9668[0m (787/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9865[0m (803/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9730[0m (792/814)
   • 中间层1准确率: [96m0.8980[0m (731/814)
   • 中间层2准确率: [96m0.9730[0m (792/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9828[0m (800/814)
   • 中间层1准确率: [96m0.9103[0m (741/814)
   • 中间层2准确率: [96m0.9840[0m (801/814)

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9287[0m (756/814)
   • 中间层1准确率: [96m0.9496[0m (773/814)
   • 中间层2准确率: [96m0.9337[0m (760/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 18/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9902[0m (806/814)
   • 中间层1准确率: [96m0.9324[0m (759/814)
   • 中间层2准确率: [96m0.9902[0m (806/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9472[0m (771/814)
   • 中间层1准确率: [96m0.9447[0m (769/814)
   • 中间层2准确率: [96m0.9570[0m (779/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 19/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9398[0m (765/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9865[0m (803/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m0.9877[0m (804/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 20/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9521[0m (775/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9939[0m (809/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 21/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9988[0m (813/814)
   • 中间层1准确率: [96m0.9558[0m (778/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 22/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 18/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 22 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9975[0m (812/814)
   • 中间层1准确率: [96m0.9681[0m (788/814)
   • 中间层2准确率: [96m0.9975[0m (812/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9975[0m (812/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 22 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 23/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 19/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 23 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9730[0m (792/814)
   • 中间层2准确率: [96m0.9963[0m (811/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9988[0m (813/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 23 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 24/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 20/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 24 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9791[0m (797/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m0.9988[0m (813/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 24 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 25/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 21/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 25 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m0.9779[0m (796/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (814/814)
   • 中间层1准确率: [96m1.0000[0m (814/814)
   • 中间层2准确率: [96m1.0000[0m (814/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 25 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7941[0m
============================================================


[1m[1;96m📅 Epoch 26/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
============================================================


[1m[1;96m📅 Epoch 22/770[0m
[1;96m----------------[0m
日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
TensorBoard日志目录已存在: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m✅ 启用标准LEGM模块:[0m
  [96m- 在conv1后增强初期特征提取[0m 🎯
  [96m- 专注于低层纹理和边缘特征捕获[0m 🔍
  [96m- 参数增加约6.5%，性能提升显著[0m 📈
  [1;95m- 推荐选择，性能与效率平衡[0m ⭐
[93m📝 使用传统特征拼接方式[0m
[1;92m🛑 启用早停机制:[0m
  [1;96m智能训练优化[0m ⚡
  [96m- 监控指标: accuracy[0m 📊
  [96m- 耐心值: 200 epochs[0m ⏳
  [96m- 改善阈值: 0.001[0m 📈
  [96m- 自动保存最佳模型[0m 💾
  [96m- 防止过拟合，提高训练效率[0m 🎯

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;93m🛑 早停配置[0m
[1;93m--------[0m
[1;96m📊 监控指标: accuracy[0m
[1;96m⏳ 耐心值: 200 epochs[0m
[1;96m📈 最小改善阈值: 0.001[0m
[1;96m🎯 模式: 最大化[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5319[0m (433/814)
   • 中间层1准确率: [96m0.4963[0m (404/814)
   • 中间层2准确率: [96m0.5160[0m (420/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.528169[0m
   • 中间损失1: [96m0.587309[0m
   • 中间损失2: [96m0.529950[0m
   • 蒸馏损失1 (KL): [95m0.029818[0m
   • 蒸馏损失2 (KL): [95m0.011007[0m
   • 特征损失1 (L2): [92m0.00057910[0m
   • 特征损失2 (L2): [92m0.00058164[0m
   • 总损失: [1;91m1.560519[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m31.30%[0m
   • 中间损失占比: [96m66.21%[0m
   • 蒸馏损失占比: [95m2.42%[0m
   • 特征损失占比: [92m0.07%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.433768[0m
   • 中间损失1: [96m0.727704[0m
   • 中间损失2: [96m0.414697[0m
   • 总验证损失: [1;91m1.576169[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.528169[0m
   • 验证损失: [94m0.433768[0m
   • 损失差值: [95m0.094401[0m (训练-验证)
   • 损失比值: [96m1.2176[0m (训练/验证)
   • 过拟合状态: [1;93m⚠️ 轻微过拟合[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6471[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.6471 (最佳: 0.6471)[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

训练过程出错: name 'save_path' is not defined
详细错误信息: Traceback (most recent call last):
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSANCASME2.py", line 1097, in <module>
    results_dict = main_SKD_TSTSAN_with_Aug_with_SKD(config)
  File "/home/<USER>/data/ajq/SKD-TSTSAN-new/train_classify_SKD_TSTSAN_functions_CASME2.py", line 2174, in main_SKD_TSTSAN_with_Aug_with_SKD
    best_model_path = os.path.join(save_path, f'best_model_{n_subName}.pth')
NameError: name 'save_path' is not defined


【实验结果报告】
================================================================================

================================================================================
【实验结果报告 - 5分类 (快乐、惊讶、厌恶、压抑、其他)】
================================================================================

【基本信息】
实验名称: SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1
分类方案: 5分类 (快乐、惊讶、厌恶、压抑、其他)
时间: 2025-07-25 13:36:25
总训练时间: 00:00:20
数据集: CASME2_LOSO_full


【系统环境】
操作系统: 💻 Linux 6.8.0-1030-nvidia
处理器: ⚡ 32核心处理器
内存: 🧠 62.7GB RAM
GPU: 🚀 NVIDIA A800 80GB PCIe (79.3GB)
GPU数量: 1
训练模式: 单设备

【模型配置】
模型架构: SKD_TSTSAN
分类数量: 5
是否使用预训练: 是
预训练模型: SKD_TSTSAN_CK+_pretrain_experiment_class7_pretrain_conv3_bior2.2_L2_legm1.pth
是否使用COCO预训练: 是
是否保存模型: 是

【训练参数】
学习率: 0.001
基础批次大小: 32
最大迭代次数: 20000
损失函数: FocalLoss_weighted
随机种子: 1337

【GPU性能配置】
CUDNN Benchmark: 禁用
CUDNN Deterministic: 启用
混合精度训练: 禁用
TF32加速: 启用
BatchNorm修复: 禁用
卷积类型: 小波变换卷积

【数据增强配置】
使用训练数据增强: 是
使用测试数据增强: 是
旋转角度范围: 3,8
训练增强倍数: 6,8,3,8,2
测试增强倍数: 6,8,3,8,2
测试数据镜像训练: 启用
镜像训练受试者: sub02,sub05

【蒸馏参数】
温度: 3
α值: 0.1
β值: 1e-06
数据增强系数: 2

【训练结果】
总体性能:
- UF1分数: 0.0000
- UAR分数: 0.0000

【各表情类别准确率】
--------------------------------------------------
- 暂无各类别准确率数据


【各受试者评估结果】
--------------------------------------------------
--------------------------------------------------

详细统计:
   受试者        UF1        UAR        准确率    
--------------------------------------------------
--------------------------------------------------
    平均        nan        nan        nan    

================================================================================

================================================================================

【邮件通知】正在发送训练结果...

【邮件通知】邮件发送成功!
- 发件人: <EMAIL>
- 收件人: <EMAIL>
- 主题: [5分类 (快乐、惊讶、厌恶、压抑、其他)实验总结] SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1 - UF1=0.0000, UAR=0.0000

正在关闭日志系统...
