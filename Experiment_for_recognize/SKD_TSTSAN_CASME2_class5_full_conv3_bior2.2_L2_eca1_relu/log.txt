日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m📝 使用传统特征拼接方式[0m
[93m⏰ 使用固定训练轮数[0m

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;92m🚀 GPU数据预加载[0m
[1;92m------------[0m
[1;96m📤 正在将所有数据预加载到GPU内存...[0m
[1;96m📊 训练数据大小: 0.27 GB[0m
[1;96m📊 测试数据大小: 0.06 GB[0m
[1;96m📊 总数据大小: 0.32 GB[0m
[1;96m⬆️ 正在上传训练数据到GPU...[0m
[1;96m⬆️ 正在上传测试数据到GPU...[0m
[1;92m✅ 数据预加载到GPU完成！[0m
[1;96m💾 GPU内存使用: GPU0: 0.3GB/79.3GB (0.4%)[0m
[1;96m⚡ GPU预加载模式：数据传输将极大加速！[0m

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 禁用[0m
[1;96m📈 Epoch损失统计: 禁用[0m
[1;96m📉 验证损失统计: 禁用[0m
[1;96m🚨 过拟合监控: 禁用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m❌ Epoch损失统计将显示: 否[0m
[1;96m❌ 验证损失统计将显示: 否[0m
[1;96m❌ 过拟合监控将显示: 否[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5651[0m (460/814)
   • 中间层1准确率: [96m0.5025[0m (409/814)
   • 中间层2准确率: [96m0.5504[0m (448/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
📊 [1mUF1:[0m [1;92m0.2468[0m | [1mUAR:[0m [1;92m0.4143[0m
🥇 [1m最佳UF1:[0m [1;93m0.0000[0m | [1m最佳UAR:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.4118 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.2468 | UAR: 0.4143 | 准确率: 0.4118[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7850[0m (639/814)
   • 中间层1准确率: [96m0.7420[0m (604/814)
   • 中间层2准确率: [96m0.7715[0m (628/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.4118[0m
📊 [1mUF1:[0m [1;92m0.2922[0m | [1mUAR:[0m [1;96m0.4000[0m
🥇 [1m最佳UF1:[0m [1;93m0.2468[0m | [1m最佳UAR:[0m [1;93m0.4143[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.4412)但UAR下降(0.4000↓)，不保存[0m

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8489[0m (691/814)
   • 中间层1准确率: [96m0.8059[0m (656/814)
   • 中间层2准确率: [96m0.8391[0m (683/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.4118[0m
📊 [1mUF1:[0m [1;92m0.5333[0m | [1mUAR:[0m [1;92m0.7429[0m
🥇 [1m最佳UF1:[0m [1;93m0.2468[0m | [1m最佳UAR:[0m [1;93m0.4143[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.6765 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.5333 | UAR: 0.7429 | 准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu/sub17/sub17.pth

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8550[0m (696/814)
   • 中间层1准确率: [96m0.8268[0m (673/814)
   • 中间层2准确率: [96m0.8624[0m (702/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.3111[0m | [1mUAR:[0m [1;96m0.4206[0m
🥇 [1m最佳UF1:[0m [1;93m0.5333[0m | [1m最佳UAR:[0m [1;93m0.7429[0m
============================================================


[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9042[0m (736/814)
   • 中间层1准确率: [96m0.8796[0m (716/814)
   • 中间层2准确率: [96m0.9128[0m (743/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5100[0m | [1mUAR:[0m [1;96m0.5937[0m
🥇 [1m最佳UF1:[0m [1;93m0.5333[0m | [1m最佳UAR:[0m [1;93m0.7429[0m
============================================================


[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9496[0m (773/814)
   • 中间层1准确率: [96m0.9312[0m (758/814)
   • 中间层2准确率: [96m0.9545[0m (777/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4348[0m | [1mUAR:[0m [1;96m0.4778[0m
🥇 [1m最佳UF1:[0m [1;93m0.5333[0m | [1m最佳UAR:[0m [1;93m0.7429[0m
============================================================


[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9717[0m (791/814)
   • 中间层1准确率: [96m0.9337[0m (760/814)
   • 中间层2准确率: [96m0.9705[0m (790/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4689[0m | [1mUAR:[0m [1;96m0.6127[0m
🥇 [1m最佳UF1:[0m [1;93m0.5333[0m | [1m最佳UAR:[0m [1;93m0.7429[0m
============================================================


[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9730[0m (792/814)
   • 中间层1准确率: [96m0.9496[0m (773/814)
   • 中间层2准确率: [96m0.9717[0m (791/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5325[0m | [1mUAR:[0m [1;96m0.6651[0m
🥇 [1m最佳UF1:[0m [1;93m0.5333[0m | [1m最佳UAR:[0m [1;93m0.7429[0m
============================================================


[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9631[0m (784/814)
   • 中间层1准确率: [96m0.9398[0m (765/814)
   • 中间层2准确率: [96m0.9631[0m (784/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.5318[0m | [1mUAR:[0m [1;96m0.7048[0m
🥇 [1m最佳UF1:[0m [1;93m0.5333[0m | [1m最佳UAR:[0m [1;93m0.7429[0m
============================================================


[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9681[0m (788/814)
   • 中间层1准确率: [96m0.9582[0m (780/814)
   • 中间层2准确率: [96m0.9717[0m (791/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;92m0.6456[0m | [1mUAR:[0m [1;96m0.7016[0m
🥇 [1m最佳UF1:[0m [1;93m0.5333[0m | [1m最佳UAR:[0m [1;93m0.7429[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.7647)但UAR下降(0.7016↓)，不保存[0m

[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9582[0m (780/814)
   • 中间层1准确率: [96m0.9484[0m (772/814)
   • 中间层2准确率: [96m0.9631[0m (784/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.2187[0m | [1mUAR:[0m [1;96m0.2778[0m
🥇 [1m最佳UF1:[0m [1;93m0.5333[0m | [1m最佳UAR:[0m [1;93m0.7429[0m
============================================================


[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9607[0m (782/814)
   • 中间层1准确率: [96m0.9398[0m (765/814)
   • 中间层2准确率: [96m0.9521[0m (775/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4254[0m | [1mUAR:[0m [1;96m0.4683[0m
🥇 [1m最佳UF1:[0m [1;93m0.5333[0m | [1m最佳UAR:[0m [1;93m0.7429[0m
============================================================


[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9730[0m (792/814)
   • 中间层1准确率: [96m0.9595[0m (781/814)
   • 中间层2准确率: [96m0.9730[0m (792/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.3991[0m | [1mUAR:[0m [1;96m0.5889[0m
🥇 [1m最佳UF1:[0m [1;93m0.5333[0m | [1m最佳UAR:[0m [1;93m0.7429[0m
============================================================


[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9803[0m (798/814)
   • 中间层1准确率: [96m0.9570[0m (779/814)
   • 中间层2准确率: [96m0.9828[0m (800/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;92m0.5560[0m | [1mUAR:[0m [1;96m0.6508[0m
🥇 [1m最佳UF1:[0m [1;93m0.5333[0m | [1m最佳UAR:[0m [1;93m0.7429[0m
============================================================


[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9693[0m (789/814)
   • 中间层1准确率: [96m0.9496[0m (773/814)
   • 中间层2准确率: [96m0.9619[0m (783/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.2977[0m | [1mUAR:[0m [1;96m0.4508[0m
🥇 [1m最佳UF1:[0m [1;93m0.5333[0m | [1m最佳UAR:[0m [1;93m0.7429[0m
============================================================


[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m
