日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: gated
✅ 三模态融合模块初始化完成
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m🔥 启用三模态特征融合:[0m
  [1;96m门控融合[0m 🚪
  [96m- 自适应调整模态权重[0m ⚖️
  [96m- 轻量级高效设计[0m ⚡
[1;92m🛑 启用早停机制:[0m
  [1;96m智能训练优化[0m ⚡
  [96m- 监控指标: accuracy[0m 📊
  [96m- 耐心值: 300 epochs[0m ⏳
  [96m- 改善阈值: 0.001[0m 📈
  [96m- 自动保存最佳模型[0m 💾
  [96m- 防止过拟合，提高训练效率[0m 🎯

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;92m🚀 GPU数据预加载[0m
[1;92m------------[0m
[1;96m📤 正在将所有数据预加载到GPU内存...[0m
[1;96m📊 训练数据大小: 0.27 GB[0m
[1;96m📊 测试数据大小: 0.06 GB[0m
[1;96m📊 总数据大小: 0.32 GB[0m
[1;96m⬆️ 正在上传训练数据到GPU...[0m
[1;96m⬆️ 正在上传测试数据到GPU...[0m
[1;92m✅ 数据预加载到GPU完成！[0m
[1;96m💾 GPU内存使用: GPU0: 0.3GB/79.3GB (0.4%)[0m
[1;96m⚡ GPU预加载模式：数据传输将极大加速！[0m

[1m[1;93m🛑 早停配置[0m
[1;93m--------[0m
[1;96m📊 监控指标: accuracy[0m
[1;96m⏳ 耐心值: 300 epochs[0m
[1;96m📈 最小改善阈值: 0.001[0m
[1;96m🎯 模式: 最大化[0m

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 禁用[0m
[1;96m📈 Epoch损失统计: 禁用[0m
[1;96m📉 验证损失统计: 禁用[0m
[1;96m🚨 过拟合监控: 禁用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m❌ Epoch损失统计将显示: 否[0m
[1;96m❌ 验证损失统计将显示: 否[0m
[1;96m❌ 过拟合监控将显示: 否[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.3870[0m (315/814)
   • 中间层1准确率: [96m0.4042[0m (329/814)
   • 中间层2准确率: [96m0.4054[0m (330/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
📊 [1mUF1:[0m [1;92m0.3708[0m | [1mUAR:[0m [1;92m0.4857[0m
🥇 [1m最佳UF1:[0m [1;93m0.0000[0m | [1m最佳UAR:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.4412 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.3708 | UAR: 0.4857 | 准确率: 0.4412[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.4412 (最佳: 0.4412)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.6916[0m (563/814)
   • 中间层1准确率: [96m0.6794[0m (553/814)
   • 中间层2准确率: [96m0.7150[0m (582/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.4412[0m
📊 [1mUF1:[0m [1;92m0.5069[0m | [1mUAR:[0m [1;92m0.5635[0m
🥇 [1m最佳UF1:[0m [1;93m0.3708[0m | [1m最佳UAR:[0m [1;93m0.4857[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.6471 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.5069 | UAR: 0.5635 | 准确率: 0.6471[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.6471 (最佳: 0.6471)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7543[0m (614/814)
   • 中间层1准确率: [96m0.7273[0m (592/814)
   • 中间层2准确率: [96m0.7592[0m (618/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;96m0.2933[0m | [1mUAR:[0m [1;96m0.3190[0m
🥇 [1m最佳UF1:[0m [1;93m0.5069[0m | [1m最佳UAR:[0m [1;93m0.5635[0m
============================================================

[1;96m🔄 ⏳ 无改善 (1/300) 剩余耐心: 299[0m

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7998[0m (651/814)
   • 中间层1准确率: [96m0.8133[0m (662/814)
   • 中间层2准确率: [96m0.8022[0m (653/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.6471[0m
📊 [1mUF1:[0m [1;92m0.5998[0m | [1mUAR:[0m [1;92m0.6444[0m
🥇 [1m最佳UF1:[0m [1;93m0.5069[0m | [1m最佳UAR:[0m [1;93m0.5635[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.7059 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.5998 | UAR: 0.6444 | 准确率: 0.7059[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.7059 (最佳: 0.7059)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8342[0m (679/814)
   • 中间层1准确率: [96m0.8010[0m (652/814)
   • 中间层2准确率: [96m0.8231[0m (670/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
📊 [1mUF1:[0m [1;96m0.2922[0m | [1mUAR:[0m [1;96m0.3270[0m
🥇 [1m最佳UF1:[0m [1;93m0.5998[0m | [1m最佳UAR:[0m [1;93m0.6444[0m
============================================================

[1;96m🔄 ⏳ 无改善 (1/300) 剩余耐心: 299[0m

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8059[0m (656/814)
   • 中间层1准确率: [96m0.8120[0m (661/814)
   • 中间层2准确率: [96m0.8047[0m (655/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
📊 [1mUF1:[0m [1;96m0.5441[0m | [1mUAR:[0m [1;96m0.5921[0m
🥇 [1m最佳UF1:[0m [1;93m0.5998[0m | [1m最佳UAR:[0m [1;93m0.6444[0m
============================================================

[1;96m🔄 ⏳ 无改善 (2/300) 剩余耐心: 298[0m

[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7936[0m (646/814)
   • 中间层1准确率: [96m0.8145[0m (663/814)
   • 中间层2准确率: [96m0.8243[0m (671/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
📊 [1mUF1:[0m [1;96m0.5264[0m | [1mUAR:[0m [1;96m0.6000[0m
🥇 [1m最佳UF1:[0m [1;93m0.5998[0m | [1m最佳UAR:[0m [1;93m0.6444[0m
============================================================

[1;96m🔄 ⏳ 无改善 (3/300) 剩余耐心: 297[0m

[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8391[0m (683/814)
   • 中间层1准确率: [96m0.8452[0m (688/814)
   • 中间层2准确率: [96m0.8440[0m (687/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
📊 [1mUF1:[0m [1;96m0.4417[0m | [1mUAR:[0m [1;96m0.5333[0m
🥇 [1m最佳UF1:[0m [1;93m0.5998[0m | [1m最佳UAR:[0m [1;93m0.6444[0m
============================================================

[1;96m🔄 ⏳ 无改善 (4/300) 剩余耐心: 296[0m

[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8428[0m (686/814)
   • 中间层1准确率: [96m0.8440[0m (687/814)
   • 中间层2准确率: [96m0.8268[0m (673/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
📊 [1mUF1:[0m [1;92m0.6427[0m | [1mUAR:[0m [1;96m0.6127[0m
🥇 [1m最佳UF1:[0m [1;93m0.5998[0m | [1m最佳UAR:[0m [1;93m0.6444[0m
============================================================

[1;96m🔄 ⏳ 无改善 (5/300) 剩余耐心: 295[0m

[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8464[0m (689/814)
   • 中间层1准确率: [96m0.8391[0m (683/814)
   • 中间层2准确率: [96m0.8354[0m (680/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
📊 [1mUF1:[0m [1;96m0.5989[0m | [1mUAR:[0m [1;96m0.5794[0m
🥇 [1m最佳UF1:[0m [1;93m0.5998[0m | [1m最佳UAR:[0m [1;93m0.6444[0m
============================================================

[1;96m🔄 ⏳ 无改善 (6/300) 剩余耐心: 294[0m

[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8649[0m (704/814)
   • 中间层1准确率: [96m0.8317[0m (677/814)
   • 中间层2准确率: [96m0.8538[0m (695/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m
📊 [1mUF1:[0m [1;92m0.6405[0m | [1mUAR:[0m [1;92m0.7016[0m
🥇 [1m最佳UF1:[0m [1;93m0.5998[0m | [1m最佳UAR:[0m [1;93m0.6444[0m
============================================================

[1;92m💾 保存最佳模型，UF1和UAR都显著提升: UF1(0.6405↑) UAR(0.7016↑)[0m
[1;96m📊 UF1: 0.6405 | UAR: 0.7016 | 准确率: 0.6176[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth
[1;96m🔄 ⏳ 无改善 (7/300) 剩余耐心: 293[0m

[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8759[0m (713/814)
   • 中间层1准确率: [96m0.8612[0m (701/814)
   • 中间层2准确率: [96m0.8550[0m (696/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m
📊 [1mUF1:[0m [1;92m0.6611[0m | [1mUAR:[0m [1;96m0.6667[0m
🥇 [1m最佳UF1:[0m [1;93m0.6405[0m | [1m最佳UAR:[0m [1;93m0.7016[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.6471)但UAR下降(0.6667↓)，不保存[0m
[1;96m🔄 ⏳ 无改善 (8/300) 剩余耐心: 292[0m

[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8796[0m (716/814)
   • 中间层1准确率: [96m0.8870[0m (722/814)
   • 中间层2准确率: [96m0.8894[0m (724/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m
📊 [1mUF1:[0m [1;96m0.5441[0m | [1mUAR:[0m [1;96m0.6016[0m
🥇 [1m最佳UF1:[0m [1;93m0.6405[0m | [1m最佳UAR:[0m [1;93m0.7016[0m
============================================================

[1;96m🔄 ⏳ 无改善 (9/300) 剩余耐心: 291[0m

[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9079[0m (739/814)
   • 中间层1准确率: [96m0.9042[0m (736/814)
   • 中间层2准确率: [96m0.9042[0m (736/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m
📊 [1mUF1:[0m [1;96m0.6241[0m | [1mUAR:[0m [1;96m0.6825[0m
🥇 [1m最佳UF1:[0m [1;93m0.6405[0m | [1m最佳UAR:[0m [1;93m0.7016[0m
============================================================

[1;96m🔄 ⏳ 无改善 (10/300) 剩余耐心: 290[0m

[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8673[0m (706/814)
   • 中间层1准确率: [96m0.8612[0m (701/814)
   • 中间层2准确率: [96m0.8710[0m (709/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m
📊 [1mUF1:[0m [1;96m0.6200[0m | [1mUAR:[0m [1;96m0.6794[0m
🥇 [1m最佳UF1:[0m [1;93m0.6405[0m | [1m最佳UAR:[0m [1;93m0.7016[0m
============================================================

[1;96m🔄 ⏳ 无改善 (11/300) 剩余耐心: 289[0m

[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8477[0m (690/814)
   • 中间层1准确率: [96m0.8317[0m (677/814)
   • 中间层2准确率: [96m0.8464[0m (689/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m
📊 [1mUF1:[0m [1;92m0.6567[0m | [1mUAR:[0m [1;96m0.6571[0m
🥇 [1m最佳UF1:[0m [1;93m0.6405[0m | [1m最佳UAR:[0m [1;93m0.7016[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.7059)但UAR下降(0.6571↓)，不保存[0m
[1;96m🔄 ⏳ 无改善 (12/300) 剩余耐心: 288[0m

[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8845[0m (720/814)
   • 中间层1准确率: [96m0.8710[0m (709/814)
   • 中间层2准确率: [96m0.8808[0m (717/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m
📊 [1mUF1:[0m [1;96m0.5119[0m | [1mUAR:[0m [1;96m0.5889[0m
🥇 [1m最佳UF1:[0m [1;93m0.6405[0m | [1m最佳UAR:[0m [1;93m0.7016[0m
============================================================

[1;96m🔄 ⏳ 无改善 (13/300) 剩余耐心: 287[0m

[1m[1;96m📅 Epoch 18/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9103[0m (741/814)
   • 中间层1准确率: [96m0.8759[0m (713/814)
   • 中间层2准确率: [96m0.9091[0m (740/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m
📊 [1mUF1:[0m [1;96m0.5645[0m | [1mUAR:[0m [1;96m0.5698[0m
🥇 [1m最佳UF1:[0m [1;93m0.6405[0m | [1m最佳UAR:[0m [1;93m0.7016[0m
============================================================

[1;96m🔄 ⏳ 无改善 (14/300) 剩余耐心: 286[0m

[1m[1;96m📅 Epoch 19/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9128[0m (743/814)
   • 中间层1准确率: [96m0.9115[0m (742/814)
   • 中间层2准确率: [96m0.9251[0m (753/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m
📊 [1mUF1:[0m [1;96m0.5231[0m | [1mUAR:[0m [1;96m0.6254[0m
🥇 [1m最佳UF1:[0m [1;93m0.6405[0m | [1m最佳UAR:[0m [1;93m0.7016[0m
============================================================

[1;96m🔄 ⏳ 无改善 (15/300) 剩余耐心: 285[0m

[1m[1;96m📅 Epoch 20/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9668[0m (787/814)
   • 中间层1准确率: [96m0.9496[0m (773/814)
   • 中间层2准确率: [96m0.9644[0m (785/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m
📊 [1mUF1:[0m [1;96m0.5359[0m | [1mUAR:[0m [1;96m0.6016[0m
🥇 [1m最佳UF1:[0m [1;93m0.6405[0m | [1m最佳UAR:[0m [1;93m0.7016[0m
============================================================

[1;96m🔄 ⏳ 无改善 (16/300) 剩余耐心: 284[0m

[1m[1;96m📅 Epoch 21/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9582[0m (780/814)
   • 中间层1准确率: [96m0.9668[0m (787/814)
   • 中间层2准确率: [96m0.9656[0m (786/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m
📊 [1mUF1:[0m [1;92m0.6540[0m | [1mUAR:[0m [1;92m0.7048[0m
🥇 [1m最佳UF1:[0m [1;93m0.6405[0m | [1m最佳UAR:[0m [1;93m0.7016[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.6765 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.6540 | UAR: 0.7048 | 准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/casme2c5普加门控_class5_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth
[1;96m🔄 ⏳ 无改善 (17/300) 剩余耐心: 283[0m

[1m[1;96m📅 Epoch 22/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 22 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9287[0m (756/814)
   • 中间层1准确率: [96m0.9459[0m (770/814)
   • 中间层2准确率: [96m0.9251[0m (753/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 22 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.6126[0m | [1mUAR:[0m [1;96m0.6365[0m
🥇 [1m最佳UF1:[0m [1;93m0.6540[0m | [1m最佳UAR:[0m [1;93m0.7048[0m
============================================================

[1;96m🔄 ⏳ 无改善 (18/300) 剩余耐心: 282[0m

[1m[1;96m📅 Epoch 23/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 23 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9447[0m (769/814)
   • 中间层1准确率: [96m0.9631[0m (784/814)
   • 中间层2准确率: [96m0.9447[0m (769/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.4GB/79.3GB (0.5%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 23 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2647[0m (9/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m
📊 [1mUF1:[0m [1;96m0.4230[0m | [1mUAR:[0m [1;96m0.5222[0m
🥇 [1m最佳UF1:[0m [1;93m0.6540[0m | [1m最佳UAR:[0m [1;93m0.7048[0m
============================================================

[1;96m🔄 ⏳ 无改善 (19/300) 剩余耐心: 281[0m

[1m[1;96m📅 Epoch 24/770[0m
[1;96m----------------[0m
