日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (CK+数据集7分类)[0m
[1;96m🎯 类别权重: ['0.1128', '0.2820', '0.0860', '0.2031', '0.0736', '0.1813', '0.0612'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: S050
========================================
组别: 中等样本组(5-8个样本) - 8个受试者
正在加载训练数据...
正在加载测试数据...

S050测试标签: [4, 0, 6, 3]
创建受试者目录: S050
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CK+_pretrain_experiment_class7_pretrain_conv3_bior2.2_L1_eca1/S050/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CK+_pretrain_experiment_class7_pretrain_conv3_bior2.2_L1_eca1/S050/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=1
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 1)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用跨分支交互[0m
  [96m- 保持原始三分支独立处理[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡
  [96m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载COCO预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 251 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6672[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 2/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9557[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.5000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.5000[0m

[1m[1;96m📅 Epoch 3/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9676[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.5000[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.5000[0m

[1m[1;96m📅 Epoch 4/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9708[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.5000[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.5000[0m

[1m[1;96m📅 Epoch 5/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9834[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7500[0m | [1m最佳准确率:[0m [1;93m0.5000[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 6/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9866[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 7/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9842[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 8/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9715[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 9/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9858[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 10/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9842[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 11/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9771[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 12/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9834[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 13/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9874[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 14/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 15/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9992[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 16/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9984[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 17/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9652[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 18/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9921[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 19/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9968[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 20/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9802[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 21/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9739[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 22/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9968[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 23/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 24/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9992[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 25/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9929[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 26/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 27/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9818[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 28/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9881[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 29/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 30/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9953[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 31/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9676[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 32/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9921[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 33/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 34/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9960[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 35/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9992[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 36/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9968[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 37/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9968[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 38/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9929[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 39/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9747[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 40/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9897[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 41/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 42/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9992[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 43/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 44/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9992[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 45/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9976[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 46/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9881[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 47/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9842[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 48/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9881[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 49/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9984[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 50/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9992[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 51/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9976[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 52/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9992[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 53/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9976[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 54/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9929[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 55/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9937[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 56/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9913[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 57/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9976[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 58/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9874[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m

[1m[1;96m📅 Epoch 59/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9945[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 60/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9992[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 61/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9968[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 62/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9992[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 63/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9984[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7500[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 0.7500[0m

[1m[1;96m📅 Epoch 64/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m1.0000[0m | [1m最佳准确率:[0m [1;93m0.7500[0m
[1;96m📈 受试者 S050 最佳准确率更新: 1.0000[0m
[1m[1;95m🏆 🎉 达到完美准确率，提前结束训练！ 🎉[0m
当前主体训练完成
最佳预测结果: [4, 0, 6, 3]
真实标签: [4, 0, 6, 3]
当前评估结果:

愤怒(anger)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
蔑视(contempt): 没有样本
厌恶(disgust): 没有样本

恐惧(fear)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

快乐(happiness)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
悲伤(sadness): 没有样本

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000

愤怒(anger)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
蔑视(contempt): 没有样本
厌恶(disgust): 没有样本

恐惧(fear)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

快乐(happiness)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000
悲伤(sadness): 没有样本

惊讶(surprise)评估结果:
样本数: 1
TP: 1, FP: 0, FN: 0, TN: 3
精确率: 1.0000, 召回率: 1.0000, F1分数: 1.0000

总体评估结果:
UF1: 1.0000
UAR: 1.0000
UF1: 1.0 | UAR: 1.0
最佳 UF1: 1.0 | 最佳 UAR: 1.0

【数据增强统计】
标签 0: 增强了 132 个样本
标签 1: 增强了 162 个样本
标签 2: 增强了 118 个样本
标签 3: 增强了 144 个样本
标签 4: 增强了 136 个样本
标签 5: 增强了 168 个样本
标签 6: 增强了 82 个样本

========================================
【当前处理受试者】: S051
========================================
组别: 中等样本组(5-8个样本) - 8个受试者
正在加载训练数据...
正在加载测试数据...

S051测试标签: [2, 6]
创建受试者目录: S051
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CK+_pretrain_experiment_class7_pretrain_conv3_bior2.2_L1_eca1/S051/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CK+_pretrain_experiment_class7_pretrain_conv3_bior2.2_L1_eca1/S051/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=1
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 1)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[93m🚫 未启用跨分支交互[0m
  [96m- 保持原始三分支独立处理[0m 📦
  [96m- 最快推理速度，最低内存占用[0m ⚡
  [96m- 传统架构，稳定可靠[0m 🔧

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载COCO预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 251 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6936[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 2/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9491[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 3/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9773[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 4/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9859[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 5/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9749[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 6/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9859[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 7/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9867[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 8/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9906[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 9/251[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9914[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 10/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9859[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 11/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9875[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 12/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9843[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 13/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9953[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 14/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9804[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 15/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9765[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 16/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9961[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 17/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9898[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 18/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9969[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 19/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9984[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 20/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9953[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 21/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9835[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 22/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9851[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 23/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9937[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 24/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9890[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 25/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9969[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.0000[0m

[1m[1;96m📅 Epoch 26/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9961[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.5000[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
[1;96m📈 受试者 S051 最佳准确率更新: 0.5000[0m

[1m[1;96m📅 Epoch 27/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9828[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.5000[0m

[1m[1;96m📅 Epoch 28/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9898[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.5000[0m

[1m[1;96m📅 Epoch 29/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9890[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.5000[0m

[1m[1;96m📅 Epoch 30/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9929[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.5000[0m

[1m[1;96m📅 Epoch 31/251[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9976[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.0000[0m | [1m最佳准确率:[0m [1;93m0.5000[0m

[1m[1;96m📅 Epoch 32/251[0m
[1;96m----------------[0m
正在关闭TensorBoard写入器...
TensorBoard写入器已关闭

正在关闭日志系统...
