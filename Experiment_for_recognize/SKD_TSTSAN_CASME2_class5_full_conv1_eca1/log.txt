日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 标准卷积 模型...[0m
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
📝 使用传统特征拼接方式
[96m使用标准卷积层[0m 🔧
[1;92m✅ 启用标准LEGM模块:[0m
  [96m- 在conv1后增强初期特征提取[0m 🎯
  [96m- 专注于低层纹理和边缘特征捕获[0m 🔍
  [96m- 参数增加约6.5%，性能提升显著[0m 📈
  [1;95m- 推荐选择，性能与效率平衡[0m ⭐
[93m📝 使用传统特征拼接方式[0m
[1;92m🛑 启用早停机制:[0m
  [1;96m智能训练优化[0m ⚡
  [96m- 监控指标: accuracy[0m 📊
  [96m- 耐心值: 200 epochs[0m ⏳
  [96m- 改善阈值: 0.001[0m 📈
  [96m- 自动保存最佳模型[0m 💾
  [96m- 防止过拟合，提高训练效率[0m 🎯

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;93m🛑 早停配置[0m
[1;93m--------[0m
[1;96m📊 监控指标: accuracy[0m
[1;96m⏳ 耐心值: 200 epochs[0m
[1;96m📈 最小改善阈值: 0.001[0m
[1;96m🎯 模式: 最大化[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5344[0m (435/814)
   • 中间层1准确率: [96m0.5012[0m (408/814)
   • 中间层2准确率: [96m0.5197[0m (423/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.525010[0m
   • 中间损失1: [96m0.591840[0m
   • 中间损失2: [96m0.544009[0m
   • 蒸馏损失1 (KL): [95m0.031982[0m
   • 蒸馏损失2 (KL): [95m0.015612[0m
   • 特征损失1 (L2): [92m0.00059584[0m
   • 特征损失2 (L2): [92m0.00058086[0m
   • 总损失: [1;91m1.575017[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m30.71%[0m
   • 中间损失占比: [96m66.44%[0m
   • 蒸馏损失占比: [95m2.78%[0m
   • 特征损失占比: [92m0.07%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m1.135648[0m
   • 中间损失1: [96m0.949220[0m
   • 中间损失2: [96m1.169248[0m
   • 总验证损失: [1;91m3.254116[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.525010[0m
   • 验证损失: [94m1.135648[0m
   • 损失差值: [95m-0.610638[0m (训练-验证)
   • 损失比值: [96m0.4623[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.3824[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.3824 (最佳: 0.3824)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7555[0m (615/814)
   • 中间层1准确率: [96m0.6806[0m (554/814)
   • 中间层2准确率: [96m0.7543[0m (614/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.213535[0m
   • 中间损失1: [96m0.281859[0m
   • 中间损失2: [96m0.224052[0m
   • 蒸馏损失1 (KL): [95m0.058880[0m
   • 蒸馏损失2 (KL): [95m0.014924[0m
   • 特征损失1 (L2): [92m0.00071418[0m
   • 特征损失2 (L2): [92m0.00069718[0m
   • 总损失: [1;91m0.686353[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m26.87%[0m
   • 中间损失占比: [96m63.66%[0m
   • 蒸馏损失占比: [95m9.29%[0m
   • 特征损失占比: [92m0.18%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.3824[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.560441[0m
   • 中间损失1: [96m0.636900[0m
   • 中间损失2: [96m0.537334[0m
   • 总验证损失: [1;91m1.734675[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.213535[0m
   • 验证损失: [94m0.560441[0m
   • 损失差值: [95m-0.346906[0m (训练-验证)
   • 损失比值: [96m0.3810[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6176[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.6176 (最佳: 0.6176)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8157[0m (664/814)
   • 中间层1准确率: [96m0.7948[0m (647/814)
   • 中间层2准确率: [96m0.8133[0m (662/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.118172[0m
   • 中间损失1: [96m0.153715[0m
   • 中间损失2: [96m0.120793[0m
   • 蒸馏损失1 (KL): [95m0.068027[0m
   • 蒸馏损失2 (KL): [95m0.015789[0m
   • 特征损失1 (L2): [92m0.00078875[0m
   • 特征损失2 (L2): [92m0.00081622[0m
   • 总损失: [1;91m0.379077[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m24.72%[0m
   • 中间损失占比: [96m57.42%[0m
   • 蒸馏损失占比: [95m17.53%[0m
   • 特征损失占比: [92m0.34%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.6176[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.581021[0m
   • 中间损失1: [96m0.607346[0m
   • 中间损失2: [96m0.556102[0m
   • 总验证损失: [1;91m1.744468[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.118172[0m
   • 验证损失: [94m0.581021[0m
   • 损失差值: [95m-0.462848[0m (训练-验证)
   • 损失比值: [96m0.2034[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.6765 (最佳: 0.6765)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8771[0m (714/814)
   • 中间层1准确率: [96m0.8649[0m (704/814)
   • 中间层2准确率: [96m0.8722[0m (710/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.076091[0m
   • 中间损失1: [96m0.097988[0m
   • 中间损失2: [96m0.080723[0m
   • 蒸馏损失1 (KL): [95m0.075143[0m
   • 蒸馏损失2 (KL): [95m0.018677[0m
   • 特征损失1 (L2): [92m0.00087223[0m
   • 特征损失2 (L2): [92m0.00089290[0m
   • 总损失: [1;91m0.249807[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m21.72%[0m
   • 中间损失占比: [96m51.00%[0m
   • 蒸馏损失占比: [95m26.78%[0m
   • 特征损失占比: [92m0.50%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.619481[0m
   • 中间损失1: [96m0.592692[0m
   • 中间损失2: [96m0.589793[0m
   • 总验证损失: [1;91m1.801966[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.076091[0m
   • 验证损失: [94m0.619481[0m
   • 损失差值: [95m-0.543390[0m (训练-验证)
   • 损失比值: [96m0.1228[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (1/200) 剩余耐心: 199[0m

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8980[0m (731/814)
   • 中间层1准确率: [96m0.8870[0m (722/814)
   • 中间层2准确率: [96m0.8956[0m (729/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.056270[0m
   • 中间损失1: [96m0.073948[0m
   • 中间损失2: [96m0.058459[0m
   • 蒸馏损失1 (KL): [95m0.075015[0m
   • 蒸馏损失2 (KL): [95m0.021331[0m
   • 特征损失1 (L2): [92m0.00089662[0m
   • 特征损失2 (L2): [92m0.00093458[0m
   • 总损失: [1;91m0.187640[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m19.62%[0m
   • 中间损失占比: [96m46.16%[0m
   • 蒸馏损失占比: [95m33.59%[0m
   • 特征损失占比: [92m0.64%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.657205[0m
   • 中间损失1: [96m0.571440[0m
   • 中间损失2: [96m0.695103[0m
   • 总验证损失: [1;91m1.923748[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.056270[0m
   • 验证损失: [94m0.657205[0m
   • 损失差值: [95m-0.600935[0m (训练-验证)
   • 损失比值: [96m0.0856[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (2/200) 剩余耐心: 198[0m

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9287[0m (756/814)
   • 中间层1准确率: [96m0.9373[0m (763/814)
   • 中间层2准确率: [96m0.9398[0m (765/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.039459[0m
   • 中间损失1: [96m0.044253[0m
   • 中间损失2: [96m0.039023[0m
   • 蒸馏损失1 (KL): [95m0.078321[0m
   • 蒸馏损失2 (KL): [95m0.026370[0m
   • 特征损失1 (L2): [92m0.00095093[0m
   • 特征损失2 (L2): [92m0.00097358[0m
   • 总损失: [1;91m0.126361[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m17.20%[0m
   • 中间损失占比: [96m36.31%[0m
   • 蒸馏损失占比: [95m45.65%[0m
   • 特征损失占比: [92m0.84%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.734390[0m
   • 中间损失1: [96m0.924410[0m
   • 中间损失2: [96m0.777270[0m
   • 总验证损失: [1;91m2.436071[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.039459[0m
   • 验证损失: [94m0.734390[0m
   • 损失差值: [95m-0.694931[0m (训练-验证)
   • 损失比值: [96m0.0537[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (3/200) 剩余耐心: 197[0m

[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9558[0m (778/814)
   • 中间层1准确率: [96m0.9533[0m (776/814)
   • 中间层2准确率: [96m0.9509[0m (774/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.024126[0m
   • 中间损失1: [96m0.031535[0m
   • 中间损失2: [96m0.025517[0m
   • 蒸馏损失1 (KL): [95m0.070935[0m
   • 蒸馏损失2 (KL): [95m0.024458[0m
   • 特征损失1 (L2): [92m0.00097611[0m
   • 特征损失2 (L2): [92m0.00100645[0m
   • 总损失: [1;91m0.086202[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m13.51%[0m
   • 中间损失占比: [96m31.95%[0m
   • 蒸馏损失占比: [95m53.43%[0m
   • 特征损失占比: [92m1.11%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.442498[0m
   • 中间损失1: [96m0.292616[0m
   • 中间损失2: [96m0.466322[0m
   • 总验证损失: [1;91m1.201436[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.024126[0m
   • 验证损失: [94m0.442498[0m
   • 损失差值: [95m-0.418372[0m (训练-验证)
   • 损失比值: [96m0.0545[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (4/200) 剩余耐心: 196[0m

[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9484[0m (772/814)
   • 中间层1准确率: [96m0.9545[0m (777/814)
   • 中间层2准确率: [96m0.9410[0m (766/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.033700[0m
   • 中间损失1: [96m0.038302[0m
   • 中间损失2: [96m0.037091[0m
   • 蒸馏损失1 (KL): [95m0.087358[0m
   • 蒸馏损失2 (KL): [95m0.028374[0m
   • 特征损失1 (L2): [92m0.00096227[0m
   • 特征损失2 (L2): [92m0.00097798[0m
   • 总损失: [1;91m0.114229[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m14.86%[0m
   • 中间损失占比: [96m33.25%[0m
   • 蒸馏损失占比: [95m51.04%[0m
   • 特征损失占比: [92m0.86%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.873142[0m
   • 中间损失1: [96m1.122990[0m
   • 中间损失2: [96m0.956912[0m
   • 总验证损失: [1;91m2.953045[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.033700[0m
   • 验证损失: [94m0.873142[0m
   • 损失差值: [95m-0.839443[0m (训练-验证)
   • 损失比值: [96m0.0386[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (5/200) 剩余耐心: 195[0m

[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9324[0m (759/814)
   • 中间层1准确率: [96m0.9324[0m (759/814)
   • 中间层2准确率: [96m0.9410[0m (766/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.041439[0m
   • 中间损失1: [96m0.044459[0m
   • 中间损失2: [96m0.041599[0m
   • 蒸馏损失1 (KL): [95m0.108924[0m
   • 蒸馏损失2 (KL): [95m0.032791[0m
   • 特征损失1 (L2): [92m0.00093714[0m
   • 特征损失2 (L2): [92m0.00096254[0m
   • 总损失: [1;91m0.133799[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m15.28%[0m
   • 中间损失占比: [96m31.74%[0m
   • 蒸馏损失占比: [95m52.27%[0m
   • 特征损失占比: [92m0.70%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6765[0m (23/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.541610[0m
   • 中间损失1: [96m0.428473[0m
   • 中间损失2: [96m0.576382[0m
   • 总验证损失: [1;91m1.546465[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.041439[0m
   • 验证损失: [94m0.541610[0m
   • 损失差值: [95m-0.500171[0m (训练-验证)
   • 损失比值: [96m0.0765[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.6765[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/sub17.pth
[1;96m🔄 ⏳ 无改善 (6/200) 剩余耐心: 194[0m

[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9607[0m (782/814)
   • 中间层1准确率: [96m0.9644[0m (785/814)
   • 中间层2准确率: [96m0.9607[0m (782/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.021736[0m
   • 中间损失1: [96m0.023435[0m
   • 中间损失2: [96m0.022689[0m
   • 蒸馏损失1 (KL): [95m0.102692[0m
   • 蒸馏损失2 (KL): [95m0.032021[0m
   • 特征损失1 (L2): [92m0.00094742[0m
   • 特征损失2 (L2): [92m0.00098765[0m
   • 总损失: [1;91m0.076794[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m10.63%[0m
   • 中间损失占比: [96m22.55%[0m
   • 蒸馏损失占比: [95m65.87%[0m
   • 特征损失占比: [92m0.95%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.6765[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.442930[0m
   • 中间损失1: [96m0.671398[0m
   • 中间损失2: [96m0.576875[0m
   • 总验证损失: [1;91m1.691202[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.021736[0m
   • 验证损失: [94m0.442930[0m
   • 损失差值: [95m-0.421194[0m (训练-验证)
   • 损失比值: [96m0.0491[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.7059[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.7059 (最佳: 0.7059)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9619[0m (783/814)
   • 中间层1准确率: [96m0.9644[0m (785/814)
   • 中间层2准确率: [96m0.9656[0m (786/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.023963[0m
   • 中间损失1: [96m0.023821[0m
   • 中间损失2: [96m0.022311[0m
   • 蒸馏损失1 (KL): [95m0.108373[0m
   • 蒸馏损失2 (KL): [95m0.033304[0m
   • 特征损失1 (L2): [92m0.00093268[0m
   • 特征损失2 (L2): [92m0.00098447[0m
   • 总损失: [1;91m0.079552[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m11.21%[0m
   • 中间损失占比: [96m21.59%[0m
   • 蒸馏损失占比: [95m66.30%[0m
   • 特征损失占比: [92m0.90%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3235[0m (11/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m1.469534[0m
   • 中间损失1: [96m1.136833[0m
   • 中间损失2: [96m1.296033[0m
   • 总验证损失: [1;91m3.902400[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.023963[0m
   • 验证损失: [94m1.469534[0m
   • 损失差值: [95m-1.445571[0m (训练-验证)
   • 损失比值: [96m0.0163[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (1/200) 剩余耐心: 199[0m

[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9742[0m (793/814)
   • 中间层1准确率: [96m0.9668[0m (787/814)
   • 中间层2准确率: [96m0.9730[0m (792/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.020416[0m
   • 中间损失1: [96m0.020787[0m
   • 中间损失2: [96m0.021174[0m
   • 蒸馏损失1 (KL): [95m0.100232[0m
   • 蒸馏损失2 (KL): [95m0.032210[0m
   • 特征损失1 (L2): [92m0.00091291[0m
   • 特征损失2 (L2): [92m0.00096262[0m
   • 总损失: [1;91m0.071383[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m10.38%[0m
   • 中间损失占比: [96m21.33%[0m
   • 蒸馏损失占比: [95m67.33%[0m
   • 特征损失占比: [92m0.95%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.810403[0m
   • 中间损失1: [96m0.911507[0m
   • 中间损失2: [96m0.781177[0m
   • 总验证损失: [1;91m2.503087[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.020416[0m
   • 验证损失: [94m0.810403[0m
   • 损失差值: [95m-0.789987[0m (训练-验证)
   • 损失比值: [96m0.0252[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (2/200) 剩余耐心: 198[0m

[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9595[0m (781/814)
   • 中间层1准确率: [96m0.9631[0m (784/814)
   • 中间层2准确率: [96m0.9631[0m (784/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.020355[0m
   • 中间损失1: [96m0.020890[0m
   • 中间损失2: [96m0.019811[0m
   • 蒸馏损失1 (KL): [95m0.094352[0m
   • 蒸馏损失2 (KL): [95m0.035071[0m
   • 特征损失1 (L2): [92m0.00092075[0m
   • 特征损失2 (L2): [92m0.00099596[0m
   • 总损失: [1;91m0.069956[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m10.58%[0m
   • 中间损失占比: [96m21.15%[0m
   • 蒸馏损失占比: [95m67.27%[0m
   • 特征损失占比: [92m1.00%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.601499[0m
   • 中间损失1: [96m0.607879[0m
   • 中间损失2: [96m0.631526[0m
   • 总验证损失: [1;91m1.840905[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.020355[0m
   • 验证损失: [94m0.601499[0m
   • 损失差值: [95m-0.581144[0m (训练-验证)
   • 损失比值: [96m0.0338[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (3/200) 剩余耐心: 197[0m

[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9717[0m (791/814)
   • 中间层1准确率: [96m0.9705[0m (790/814)
   • 中间层2准确率: [96m0.9754[0m (794/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.016610[0m
   • 中间损失1: [96m0.020070[0m
   • 中间损失2: [96m0.015836[0m
   • 蒸馏损失1 (KL): [95m0.105278[0m
   • 蒸馏损失2 (KL): [95m0.034781[0m
   • 特征损失1 (L2): [92m0.00089451[0m
   • 特征损失2 (L2): [92m0.00097222[0m
   • 总损失: [1;91m0.062569[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m8.54%[0m
   • 中间损失占比: [96m18.47%[0m
   • 蒸馏损失占比: [95m72.03%[0m
   • 特征损失占比: [92m0.96%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m0.977161[0m
   • 中间损失1: [96m0.758891[0m
   • 中间损失2: [96m0.943321[0m
   • 总验证损失: [1;91m2.679374[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.016610[0m
   • 验证损失: [94m0.977161[0m
   • 损失差值: [95m-0.960552[0m (训练-验证)
   • 损失比值: [96m0.0170[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;92m💾 保存最佳模型，准确率: 0.7059[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv1_eca1/sub17/sub17.pth
[1;96m🔄 ⏳ 无改善 (4/200) 剩余耐心: 196[0m

[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9791[0m (797/814)
   • 中间层1准确率: [96m0.9853[0m (802/814)
   • 中间层2准确率: [96m0.9791[0m (797/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.009228[0m
   • 中间损失1: [96m0.010169[0m
   • 中间损失2: [96m0.008716[0m
   • 蒸馏损失1 (KL): [95m0.087480[0m
   • 蒸馏损失2 (KL): [95m0.035073[0m
   • 特征损失1 (L2): [92m0.00089318[0m
   • 特征损失2 (L2): [92m0.00097269[0m
   • 总损失: [1;91m0.038116[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m6.05%[0m
   • 中间损失占比: [96m12.38%[0m
   • 蒸馏损失占比: [95m80.35%[0m
   • 特征损失占比: [92m1.22%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m1.056605[0m
   • 中间损失1: [96m1.019860[0m
   • 中间损失2: [96m1.024592[0m
   • 总验证损失: [1;91m3.101057[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.009228[0m
   • 验证损失: [94m1.056605[0m
   • 损失差值: [95m-1.047377[0m (训练-验证)
   • 损失比值: [96m0.0087[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (5/200) 剩余耐心: 195[0m

[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9951[0m (810/814)
   • 中间层1准确率: [96m0.9939[0m (809/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.004574[0m
   • 中间损失1: [96m0.005275[0m
   • 中间损失2: [96m0.004884[0m
   • 蒸馏损失1 (KL): [95m0.071827[0m
   • 蒸馏损失2 (KL): [95m0.033181[0m
   • 特征损失1 (L2): [92m0.00091451[0m
   • 特征损失2 (L2): [92m0.00097435[0m
   • 总损失: [1;91m0.024057[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m3.76%[0m
   • 中间损失占比: [96m8.35%[0m
   • 蒸馏损失占比: [95m86.33%[0m
   • 特征损失占比: [92m1.55%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7059[0m

📉 [1m[1;94m验证损失统计:[0m
   • 主要损失 (CE): [1;93m1.068806[0m
   • 中间损失1: [96m0.994805[0m
   • 中间损失2: [96m1.082210[0m
   • 总验证损失: [1;91m3.145821[0m

🚨 [1m[1;91m过拟合监控 (训练 vs 验证):[0m
   • 训练损失: [93m0.004574[0m
   • 验证损失: [94m1.068806[0m
   • 损失差值: [95m-1.064232[0m (训练-验证)
   • 损失比值: [96m0.0043[0m (训练/验证)
   • 过拟合状态: [1;92m✅ 正常[0m
============================================================

[1;96m🔄 ⏳ 无改善 (6/200) 剩余耐心: 194[0m

[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9717[0m (791/814)
   • 中间层1准确率: [96m0.9791[0m (797/814)
   • 中间层2准确率: [96m0.9717[0m (791/814)

📈 [1m[1;93m损失函数统计:[0m
   • 主要损失 (CE): [1;93m0.020881[0m
   • 中间损失1: [96m0.017330[0m
   • 中间损失2: [96m0.021514[0m
   • 蒸馏损失1 (KL): [95m0.122528[0m
   • 蒸馏损失2 (KL): [95m0.037732[0m
   • 特征损失1 (L2): [92m0.00087244[0m
   • 特征损失2 (L2): [92m0.00092137[0m
   • 总损失: [1;91m0.071024[0m

🔍 [1m[1;94m损失组成分析 (用于过拟合监控):[0m
   • 主要损失占比: [1;93m9.42%[0m
   • 中间损失占比: [96m17.51%[0m
   • 蒸馏损失占比: [95m72.26%[0m
   • 特征损失占比: [92m0.81%[0m

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m
