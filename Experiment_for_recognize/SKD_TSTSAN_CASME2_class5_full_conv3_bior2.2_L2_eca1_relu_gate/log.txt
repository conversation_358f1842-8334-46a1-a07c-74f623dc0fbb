日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
=> 使用折叠除法: 8
🔥 启用三模态特征融合: gated
✅ 三模态融合模块初始化完成
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m✅ 启用标准LEGM模块:[0m
  [96m- 在conv1后增强初期特征提取[0m 🎯
  [96m- 专注于低层纹理和边缘特征捕获[0m 🔍
  [96m- 参数增加约6.5%，性能提升显著[0m 📈
  [1;95m- 推荐选择，性能与效率平衡[0m ⭐
[1;92m🔥 启用三模态特征融合:[0m
  [1;96m门控融合[0m 🚪
  [96m- 自适应调整模态权重[0m ⚖️
  [96m- 轻量级高效设计[0m ⚡
[1;92m🛑 启用早停机制:[0m
  [1;96m智能训练优化[0m ⚡
  [96m- 监控指标: accuracy[0m 📊
  [96m- 耐心值: 200 epochs[0m ⏳
  [96m- 改善阈值: 0.001[0m 📈
  [96m- 自动保存最佳模型[0m 💾
  [96m- 防止过拟合，提高训练效率[0m 🎯

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;93m🛑 早停配置[0m
[1;93m--------[0m
[1;96m📊 监控指标: accuracy[0m
[1;96m⏳ 耐心值: 200 epochs[0m
[1;96m📈 最小改善阈值: 0.001[0m
[1;96m🎯 模式: 最大化[0m

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 禁用[0m
[1;96m📈 Epoch损失统计: 禁用[0m
[1;96m📉 验证损失统计: 禁用[0m
[1;96m🚨 过拟合监控: 禁用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m❌ Epoch损失统计将显示: 否[0m
[1;96m❌ 验证损失统计将显示: 否[0m
[1;96m❌ 过拟合监控将显示: 否[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.4349[0m (354/814)
   • 中间层1准确率: [96m0.3894[0m (317/814)
   • 中间层2准确率: [96m0.4165[0m (339/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.2353[0m (8/34)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
📊 [1mUF1:[0m [1;92m0.0762[0m | [1mUAR:[0m [1;92m0.1778[0m
🥇 [1m最佳UF1:[0m [1;93m0.0000[0m | [1m最佳UAR:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，UF1+UAR综合评分: 0.1270[0m
[1;96m📊 UF1: 0.0762 | UAR: 0.1778 | 准确率: 0.2353[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.2353 (最佳: 0.2353)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.6708[0m (546/814)
   • 中间层1准确率: [96m0.6695[0m (545/814)
   • 中间层2准确率: [96m0.6708[0m (546/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.2353[0m
📊 [1mUF1:[0m [1;92m0.5256[0m | [1mUAR:[0m [1;92m0.5810[0m
🥇 [1m最佳UF1:[0m [1;93m0.0762[0m | [1m最佳UAR:[0m [1;93m0.1778[0m
============================================================

[1;92m💾 保存最佳模型，UF1+UAR综合评分: 0.5533[0m
[1;96m📊 UF1: 0.5256 | UAR: 0.5810 | 准确率: 0.5882[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.5882 (最佳: 0.5882)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7285[0m (593/814)
   • 中间层1准确率: [96m0.7052[0m (574/814)
   • 中间层2准确率: [96m0.7371[0m (600/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3529[0m (12/34)
🏆 [1m最佳准确率:[0m [1;93m0.5882[0m
📊 [1mUF1:[0m [1;96m0.2139[0m | [1mUAR:[0m [1;96m0.3048[0m
🥇 [1m最佳UF1:[0m [1;93m0.5256[0m | [1m最佳UAR:[0m [1;93m0.5810[0m
============================================================

[1;96m🔄 ⏳ 无改善 (1/200) 剩余耐心: 199[0m

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7629[0m (621/814)
   • 中间层1准确率: [96m0.7641[0m (622/814)
   • 中间层2准确率: [96m0.7592[0m (618/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.7647[0m (26/34)
🏆 [1m最佳准确率:[0m [1;93m0.5882[0m
📊 [1mUF1:[0m [1;92m0.7215[0m | [1mUAR:[0m [1;92m0.7159[0m
🥇 [1m最佳UF1:[0m [1;93m0.5256[0m | [1m最佳UAR:[0m [1;93m0.5810[0m
============================================================

[1;92m💾 保存最佳模型，UF1+UAR综合评分: 0.7187[0m
[1;96m📊 UF1: 0.7215 | UAR: 0.7159 | 准确率: 0.7647[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth
[1;96m📈 🎯 accuracy改善: 0.7647 (最佳: 0.7647)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/best_model_sub17.pth[0m

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.7801[0m (635/814)
   • 中间层1准确率: [96m0.7604[0m (619/814)
   • 中间层2准确率: [96m0.8022[0m (653/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7059[0m (24/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.4460[0m | [1mUAR:[0m [1;96m0.4810[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (1/200) 剩余耐心: 199[0m

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8133[0m (662/814)
   • 中间层1准确率: [96m0.8084[0m (658/814)
   • 中间层2准确率: [96m0.8206[0m (668/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6560[0m | [1mUAR:[0m [1;96m0.6476[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (2/200) 剩余耐心: 198[0m

[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8206[0m (668/814)
   • 中间层1准确率: [96m0.8243[0m (671/814)
   • 中间层2准确率: [96m0.8034[0m (654/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6282[0m | [1mUAR:[0m [1;96m0.7111[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (3/200) 剩余耐心: 197[0m

[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8428[0m (686/814)
   • 中间层1准确率: [96m0.8514[0m (693/814)
   • 中间层2准确率: [96m0.8428[0m (686/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5907[0m | [1mUAR:[0m [1;96m0.7000[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (4/200) 剩余耐心: 196[0m

[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8612[0m (701/814)
   • 中间层1准确率: [96m0.8526[0m (694/814)
   • 中间层2准确率: [96m0.8686[0m (707/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5820[0m | [1mUAR:[0m [1;96m0.6095[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (5/200) 剩余耐心: 195[0m

[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8624[0m (702/814)
   • 中间层1准确率: [96m0.8796[0m (716/814)
   • 中间层2准确率: [96m0.8735[0m (711/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.4075[0m | [1mUAR:[0m [1;96m0.4302[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (6/200) 剩余耐心: 194[0m

[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8710[0m (709/814)
   • 中间层1准确率: [96m0.8833[0m (719/814)
   • 中间层2准确率: [96m0.8833[0m (719/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6446[0m | [1mUAR:[0m [1;96m0.6476[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (7/200) 剩余耐心: 193[0m

[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9005[0m (733/814)
   • 中间层1准确率: [96m0.9165[0m (746/814)
   • 中间层2准确率: [96m0.9079[0m (739/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6174[0m | [1mUAR:[0m [1;96m0.6000[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (8/200) 剩余耐心: 192[0m

[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9386[0m (764/814)
   • 中间层1准确率: [96m0.9042[0m (736/814)
   • 中间层2准确率: [96m0.9324[0m (759/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6469[0m | [1mUAR:[0m [1;96m0.6206[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (9/200) 剩余耐心: 191[0m

[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8882[0m (723/814)
   • 中间层1准确率: [96m0.8808[0m (717/814)
   • 中间层2准确率: [96m0.8833[0m (719/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6253[0m | [1mUAR:[0m [1;96m0.6460[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (10/200) 剩余耐心: 190[0m

[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9201[0m (749/814)
   • 中间层1准确率: [96m0.9115[0m (742/814)
   • 中间层2准确率: [96m0.9165[0m (746/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5937[0m | [1mUAR:[0m [1;92m0.7492[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (11/200) 剩余耐心: 189[0m

[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9042[0m (736/814)
   • 中间层1准确率: [96m0.8993[0m (732/814)
   • 中间层2准确率: [96m0.8980[0m (731/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5569[0m | [1mUAR:[0m [1;96m0.5778[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (12/200) 剩余耐心: 188[0m

[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8550[0m (696/814)
   • 中间层1准确率: [96m0.8649[0m (704/814)
   • 中间层2准确率: [96m0.8673[0m (706/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5873[0m | [1mUAR:[0m [1;96m0.6619[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (13/200) 剩余耐心: 187[0m

[1m[1;96m📅 Epoch 18/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9054[0m (737/814)
   • 中间层1准确率: [96m0.8857[0m (721/814)
   • 中间层2准确率: [96m0.9054[0m (737/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6693[0m | [1mUAR:[0m [1;96m0.6651[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (14/200) 剩余耐心: 186[0m

[1m[1;96m📅 Epoch 19/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9189[0m (748/814)
   • 中间层1准确率: [96m0.8968[0m (730/814)
   • 中间层2准确率: [96m0.9128[0m (743/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.5781[0m | [1mUAR:[0m [1;96m0.5714[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (15/200) 剩余耐心: 185[0m

[1m[1;96m📅 Epoch 20/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9287[0m (756/814)
   • 中间层1准确率: [96m0.9189[0m (748/814)
   • 中间层2准确率: [96m0.9349[0m (761/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6176[0m (21/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;96m0.6827[0m | [1mUAR:[0m [1;92m0.7397[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;96m🔄 ⏳ 无改善 (16/200) 剩余耐心: 184[0m

[1m[1;96m📅 Epoch 21/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9115[0m (742/814)
   • 中间层1准确率: [96m0.9115[0m (742/814)
   • 中间层2准确率: [96m0.9103[0m (741/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7353[0m (25/34)
🏆 [1m最佳准确率:[0m [1;93m0.7647[0m
📊 [1mUF1:[0m [1;92m0.7480[0m | [1mUAR:[0m [1;92m0.8143[0m
🥇 [1m最佳UF1:[0m [1;93m0.7215[0m | [1m最佳UAR:[0m [1;93m0.7159[0m
============================================================

[1;92m💾 保存最佳模型，UF1+UAR综合评分: 0.7811[0m
[1;96m📊 UF1: 0.7480 | UAR: 0.8143 | 准确率: 0.7353[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth[0m
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv3_bior2.2_L2_eca1_relu_gate/sub17/sub17.pth
[1;96m🔄 ⏳ 无改善 (17/200) 剩余耐心: 183[0m

[1m[1;96m📅 Epoch 22/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 22 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9275[0m (755/814)
   • 中间层1准确率: [96m0.9312[0m (758/814)
   • 中间层2准确率: [96m0.9435[0m (768/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 22 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4118[0m (14/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.4082[0m | [1mUAR:[0m [1;96m0.4984[0m
🥇 [1m最佳UF1:[0m [1;93m0.7480[0m | [1m最佳UAR:[0m [1;93m0.8143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (18/200) 剩余耐心: 182[0m

[1m[1;96m📅 Epoch 23/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 23 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9447[0m (769/814)
   • 中间层1准确率: [96m0.9496[0m (773/814)
   • 中间层2准确率: [96m0.9509[0m (774/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 23 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.7023[0m | [1mUAR:[0m [1;96m0.7921[0m
🥇 [1m最佳UF1:[0m [1;93m0.7480[0m | [1m最佳UAR:[0m [1;93m0.8143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (19/200) 剩余耐心: 181[0m

[1m[1;96m📅 Epoch 24/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 24 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9201[0m (749/814)
   • 中间层1准确率: [96m0.9337[0m (760/814)
   • 中间层2准确率: [96m0.9165[0m (746/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 24 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.3836[0m | [1mUAR:[0m [1;96m0.4619[0m
🥇 [1m最佳UF1:[0m [1;93m0.7480[0m | [1m最佳UAR:[0m [1;93m0.8143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (20/200) 剩余耐心: 180[0m

[1m[1;96m📅 Epoch 25/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 25 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9324[0m (759/814)
   • 中间层1准确率: [96m0.9312[0m (758/814)
   • 中间层2准确率: [96m0.9251[0m (753/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 25 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.4516[0m | [1mUAR:[0m [1;96m0.4730[0m
🥇 [1m最佳UF1:[0m [1;93m0.7480[0m | [1m最佳UAR:[0m [1;93m0.8143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (21/200) 剩余耐心: 179[0m

[1m[1;96m📅 Epoch 26/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 26 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9472[0m (771/814)
   • 中间层1准确率: [96m0.9631[0m (784/814)
   • 中间层2准确率: [96m0.9496[0m (773/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 26 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3824[0m (13/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.5221[0m | [1mUAR:[0m [1;96m0.6175[0m
🥇 [1m最佳UF1:[0m [1;93m0.7480[0m | [1m最佳UAR:[0m [1;93m0.8143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (22/200) 剩余耐心: 178[0m

[1m[1;96m📅 Epoch 27/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 27 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9730[0m (792/814)
   • 中间层1准确率: [96m0.9619[0m (783/814)
   • 中间层2准确率: [96m0.9681[0m (788/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 27 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6471[0m (22/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.7001[0m | [1mUAR:[0m [1;96m0.7857[0m
🥇 [1m最佳UF1:[0m [1;93m0.7480[0m | [1m最佳UAR:[0m [1;93m0.8143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (23/200) 剩余耐心: 177[0m

[1m[1;96m📅 Epoch 28/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 28 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9472[0m (771/814)
   • 中间层1准确率: [96m0.9447[0m (769/814)
   • 中间层2准确率: [96m0.9545[0m (777/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 28 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5294[0m (18/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.6121[0m | [1mUAR:[0m [1;96m0.6444[0m
🥇 [1m最佳UF1:[0m [1;93m0.7480[0m | [1m最佳UAR:[0m [1;93m0.8143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (24/200) 剩余耐心: 176[0m

[1m[1;96m📅 Epoch 29/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 29 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9312[0m (758/814)
   • 中间层1准确率: [96m0.9373[0m (763/814)
   • 中间层2准确率: [96m0.9324[0m (759/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 29 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5882[0m (20/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.6194[0m | [1mUAR:[0m [1;96m0.6683[0m
🥇 [1m最佳UF1:[0m [1;93m0.7480[0m | [1m最佳UAR:[0m [1;93m0.8143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (25/200) 剩余耐心: 175[0m

[1m[1;96m📅 Epoch 30/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 30 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9681[0m (788/814)
   • 中间层1准确率: [96m0.9693[0m (789/814)
   • 中间层2准确率: [96m0.9644[0m (785/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 30 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5588[0m (19/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.5867[0m | [1mUAR:[0m [1;96m0.5937[0m
🥇 [1m最佳UF1:[0m [1;93m0.7480[0m | [1m最佳UAR:[0m [1;93m0.8143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (26/200) 剩余耐心: 174[0m

[1m[1;96m📅 Epoch 31/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 31 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9754[0m (794/814)
   • 中间层1准确率: [96m0.9730[0m (792/814)
   • 中间层2准确率: [96m0.9828[0m (800/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 31 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4412[0m (15/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.5363[0m | [1mUAR:[0m [1;96m0.5206[0m
🥇 [1m最佳UF1:[0m [1;93m0.7480[0m | [1m最佳UAR:[0m [1;93m0.8143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (27/200) 剩余耐心: 173[0m

[1m[1;96m📅 Epoch 32/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 32 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9926[0m (808/814)
   • 中间层1准确率: [96m0.9877[0m (804/814)
   • 中间层2准确率: [96m0.9951[0m (810/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 32 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (17/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.5679[0m | [1mUAR:[0m [1;96m0.5571[0m
🥇 [1m最佳UF1:[0m [1;93m0.7480[0m | [1m最佳UAR:[0m [1;93m0.8143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (28/200) 剩余耐心: 172[0m

[1m[1;96m📅 Epoch 33/770[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 33 训练统计 - sub17[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (801/814)
   • 中间层1准确率: [96m0.9816[0m (799/814)
   • 中间层2准确率: [96m0.9889[0m (805/814)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.1GB/79.3GB (0.1%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 33 验证统计 - sub17[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.4706[0m (16/34)
🏆 [1m最佳准确率:[0m [1;93m0.7353[0m
📊 [1mUF1:[0m [1;96m0.5451[0m | [1mUAR:[0m [1;96m0.5730[0m
🥇 [1m最佳UF1:[0m [1;93m0.7480[0m | [1m最佳UAR:[0m [1;93m0.8143[0m
============================================================

[1;96m🔄 ⏳ 无改善 (29/200) 剩余耐心: 171[0m

[1m[1;96m📅 Epoch 34/770[0m
[1;96m----------------[0m
