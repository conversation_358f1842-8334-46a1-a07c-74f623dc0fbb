import torch.nn as nn
import torch
import torch.nn.functional as F
import math
from motion_magnification_learning_based_master.magnet import Manipulator as MagManipulator
from motion_magnification_learning_based_master.magnet import Encoder_No_texture as MagEncoder_No_texture
from Pinwheel_shaped_Convolution import Pinwheel_shaped_Convolution
from WTConv import WTConv2d
from PRO_CODE.WTRFAConv import WTRFAConv
from PRO_CODE.WTPRFAConv import WTPRFAConv
from PRO_CODE.WTPRFAConv_Lite import WTPRFAConv_Lite
from PRO_CODE.LEGM import LEGM
from activation_selector import Mish, FReLU
from PRO_CODE.GRSA import GRSA


class GRSA_ChannelAdapter(nn.Module):
    """
    GRSA通道注意力适配器
    基于GRSA思想的简化通道注意力机制，专为微表情识别优化

    核心设计：
    1. 分组残差通道注意力：将通道分组进行独立的注意力计算
    2. 指数空间权重编码：使用非线性变换增强权重表达能力
    3. 余弦相似度注意力：提供更稳定的注意力计算
    """

    def __init__(self, channels, num_groups=2, reduction=4):
        super(GRSA_ChannelAdapter, self).__init__()
        self.channels = channels
        self.num_groups = num_groups
        self.group_channels = channels // num_groups

        # 确保通道数能被分组数整除
        assert channels % num_groups == 0, f"通道数 {channels} 必须能被分组数 {num_groups} 整除"

        # 分组残差层 - 每个组独立的线性变换
        self.group_transforms = nn.ModuleList([
            nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(self.group_channels, self.group_channels // reduction, 1, bias=False),
                nn.ReLU(inplace=True),
                nn.Conv2d(self.group_channels // reduction, self.group_channels, 1, bias=False)
            ) for _ in range(num_groups)
        ])

        # 指数空间相对位置编码 - 用于权重调制
        self.position_encoding = nn.Parameter(torch.randn(channels) * 0.02)

        # 温度参数 - 用于余弦相似度注意力
        self.temperature = nn.Parameter(torch.log(torch.tensor(10.0)))

        # 最终融合层
        self.fusion = nn.Sequential(
            nn.Conv2d(channels, channels, 1, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        """
        前向传播 - 分组残差自注意力计算

        Args:
            x: 输入特征图 [B, C, H, W]

        Returns:
            输出特征图 [B, C, H, W]
        """
        B, C, H, W = x.shape

        # 保存原始输入用于残差连接
        identity = x

        # 将输入按通道分组
        x_groups = x.view(B, self.num_groups, self.group_channels, H, W)

        # 对每个组进行独立的注意力计算
        group_outputs = []
        for i, transform in enumerate(self.group_transforms):
            group_input = x_groups[:, i]  # [B, group_channels, H, W]

            # 分组残差层计算
            group_attention = transform(group_input)  # [B, group_channels, 1, 1]

            # 应用残差连接
            group_output = group_input + group_input * group_attention
            group_outputs.append(group_output)

        # 重新组合所有分组
        x_grouped = torch.stack(group_outputs, dim=1)  # [B, num_groups, group_channels, H, W]
        x_grouped = x_grouped.view(B, C, H, W)

        # 应用指数空间位置编码
        position_weights = torch.sigmoid(self.position_encoding).view(1, C, 1, 1)
        x_positioned = x_grouped * position_weights

        # 余弦相似度注意力计算
        # 计算全局平均池化特征
        global_feat = F.adaptive_avg_pool2d(x_positioned, 1)  # [B, C, 1, 1]

        # 归一化特征用于余弦相似度计算
        global_feat_norm = F.normalize(global_feat, dim=1)
        channel_weights_norm = F.normalize(position_weights, dim=1)

        # 计算余弦相似度注意力权重
        cosine_sim = (global_feat_norm * channel_weights_norm).sum(dim=1, keepdim=True)
        max_temp = torch.log(torch.tensor(100.0, device=self.temperature.device))
        temperature = torch.clamp(self.temperature, max=max_temp).exp()
        attention_weights = torch.sigmoid(cosine_sim * temperature)

        # 最终融合
        fusion_weights = self.fusion(x_positioned * attention_weights)
        output = identity + x_positioned * fusion_weights

        return output


def gen_state_dict(weights_path):
    st = torch.load(weights_path)
    st_ks = list(st.keys())
    st_vs = list(st.values())
    state_dict = {}
    for st_k, st_v in zip(st_ks, st_vs):
        state_dict[st_k.replace('module.', '')] = st_v
    return state_dict

class ConsensusModule(torch.nn.Module):

    def __init__(self, consensus_type, dim=1):
        super(ConsensusModule, self).__init__()
        self.consensus_type = consensus_type if consensus_type != 'rnn' else 'identity'
        self.dim = dim

    def forward(self, input):
        return SegmentConsensus(self.consensus_type, self.dim)(input)

class SegmentConsensus(torch.nn.Module):

    def __init__(self, consensus_type, dim=1):
        super(SegmentConsensus, self).__init__()
        self.consensus_type = consensus_type
        self.dim = dim
        self.shape = None

    def forward(self, input_tensor):
        self.shape = input_tensor.size()
        if self.consensus_type == 'avg':
            output = input_tensor.mean(dim=self.dim, keepdim=True)
        elif self.consensus_type == 'identity':
            output = input_tensor
        else:
            output = None

        return output

class TemporalShift(nn.Module):
    def __init__(self, net, n_segment=3, n_div=8, inplace=False):
        super(TemporalShift, self).__init__()
        self.net = net
        self.n_segment = n_segment
        self.fold_div = n_div
        self.inplace = inplace
        if inplace:
            print('=> 使用原地移位...')
        print('=> 使用折叠除法: {}'.format(self.fold_div))

    def forward(self, x):
        x = self.shift(x, self.n_segment, fold_div=self.fold_div, inplace=self.inplace)
        return self.net(x)

    @staticmethod
    def shift(x, n_segment, fold_div=3, inplace=False):
        nt, c, h, w = x.size()
        n_batch = nt // n_segment
        x = x.view(n_batch, n_segment, c, h, w)

        fold = c // fold_div
        if inplace:
            raise NotImplementedError
        else:
            out = torch.zeros_like(x)
            out[:, :-1, :fold] = x[:, 1:, :fold]  # shift left
            out[:, 1:, fold: 2 * fold] = x[:, :-1, fold: 2 * fold]  # shift right
            out[:, :, 2 * fold:] = x[:, :, 2 * fold:]  # not shift

        return out.view(nt, c, h, w)

class eca_layer_2d_v2(nn.Module):
    """Constructs a ECA module.

    Args:
        channel: Number of channels of the input feature map
        k_size: Adaptive selection of kernel size
    """

    def __init__(self, channel):
        super(eca_layer_2d_v2, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        t = int(abs(math.log(channel,2)+1)/2)
        k_size = t if t%2 else (t+1)
        self.conv = nn.Conv1d(1, 1, kernel_size=k_size, padding=(k_size - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        y_avg = self.avg_pool(x)
        y_max = self.max_pool(x)

        y_avg = self.conv(y_avg.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)
        y_max = self.conv(y_max.squeeze(-1).transpose(-1, -2)).transpose(-1, -2).unsqueeze(-1)

        y = self.sigmoid(y_avg+y_max)

        return x * y.expand_as(x)

class SKD_TSTSAN(nn.Module):
    def __init__(self, out_channels=5, amp_factor=5, use_pinwheel_conv=False, use_wtconv=False,
                 use_wtrfaconv=False, use_wtprfaconv=False, use_wtprfaconv_lite=False,
                 use_legm=False, use_grsa=False, wt_type='db4', wt_levels=1,
                 use_trimodal_fusion=False, trimodal_fusion_type='ensemble', trimodal_num_heads=8,
                 use_enhanced_activation=False):
        super(SKD_TSTSAN, self).__init__()
        self.use_pinwheel_conv = use_pinwheel_conv
        self.use_wtconv = use_wtconv
        self.use_wtrfaconv = use_wtrfaconv
        self.use_wtprfaconv = use_wtprfaconv
        self.use_wtprfaconv_lite = use_wtprfaconv_lite
        self.use_legm = use_legm
        self.use_grsa = use_grsa
        self.wt_type = wt_type  # 小波类型参数
        self.wt_levels = wt_levels  # 小波变换层数参数
        self.use_enhanced_activation = use_enhanced_activation  # 激活函数选择参数

        # 保存融合配置
        self.use_trimodal_fusion = use_trimodal_fusion
        self.trimodal_fusion_type = trimodal_fusion_type

        # 打印小波配置信息
        if use_wtconv or use_wtrfaconv or use_wtprfaconv or use_wtprfaconv_lite:
            print(f"🌊 小波配置: 类型={wt_type}, 层数={wt_levels}")

        # 确保只有一种卷积类型被激活
        conv_types = [use_pinwheel_conv, use_wtconv, use_wtrfaconv, use_wtprfaconv, use_wtprfaconv_lite]
        if sum(conv_types) > 1:
            raise ValueError("只能同时启用一种卷积类型：use_pinwheel_conv, use_wtconv, use_wtrfaconv, use_wtprfaconv, use_wtprfaconv_lite")
        self.Aug_Encoder_L = MagEncoder_No_texture(dim_in=16)
        self.Aug_Encoder_S = MagEncoder_No_texture(dim_in=1)
        self.Aug_Encoder_T = MagEncoder_No_texture(dim_in=2)
        self.Aug_Manipulator_L = MagManipulator()
        self.Aug_Manipulator_S = MagManipulator()
        self.Aug_Manipulator_T = MagManipulator()

        # 根据配置选择不同的卷积层类型
        if self.use_wtprfaconv_lite:
            # WTPRFAConv_Lite要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用轻量化WTPRFAConv，大幅减少参数数量
            self.conv1_L = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_S = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_T = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
        elif self.use_wtprfaconv:
            # WTPRFAConv要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用WTPRFAConv进行小波变换风车形感受野注意力卷积，融合三种先进技术
            self.conv1_L = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_S = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv1_T = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
        elif self.use_wtrfaconv:
            # WTRFAConv要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用WTRFAConv进行小波变换感受野注意力卷积，结合多频响应和空间注意力
            self.conv1_L = WTRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            self.conv1_S = WTRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            self.conv1_T = WTRFAConv(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
        elif self.use_wtconv:
            # WTConv要求输入输出通道数相同，需要先用1x1卷积调整通道数
            self.conv1_L_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_S_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            self.conv1_T_pre = nn.Conv2d(32, 64, kernel_size=1, stride=1, padding=0)
            # 使用WTConv进行小波变换卷积，增强多频响应特征提取
            self.conv1_L = WTConv2d(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type)
            self.conv1_S = WTConv2d(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type)
            self.conv1_T = WTConv2d(in_channels=64, out_channels=64, kernel_size=5, wt_levels=self.wt_levels, wt_type=self.wt_type)
        elif self.use_pinwheel_conv:
            # 风车形卷积要求输出通道数是4的倍数，这里使用64通道
            self.conv1_L = Pinwheel_shaped_Convolution(c1=32, c2=64, k=5, s=1)
            self.conv1_S = Pinwheel_shaped_Convolution(c1=32, c2=64, k=5, s=1)
            self.conv1_T = Pinwheel_shaped_Convolution(c1=32, c2=64, k=5, s=1)
        else:
            # 使用标准卷积
            self.conv1_L = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)
            self.conv1_S = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)
            self.conv1_T = nn.Conv2d(32, out_channels=64, kernel_size=5, stride=1)

        # 激活函数选择：支持多种组合策略
        if self.use_enhanced_activation == True:
            # 浅层Mish + 深层FReLU组合
            self.shallow_activation = Mish()
            # 深层激活函数：FReLU，适合深层空间细节保留
            # conv2_L, conv3_L 输出64通道
            self.deep_activation_L_64 = FReLU(in_channels=64)
            self.deep_activation_S_64 = FReLU(in_channels=64)
            self.deep_activation_T_64 = FReLU(in_channels=64)
            # conv4_L, conv5_L 输出128通道
            self.deep_activation_L_128 = FReLU(in_channels=128)
            self.deep_activation_S_128 = FReLU(in_channels=128)
            self.deep_activation_T_128 = FReLU(in_channels=128)
            # 辅助分类器激活函数
            self.ac_activation_128 = FReLU(in_channels=128)
            print("🔥 激活函数配置: 浅层Mish + 深层FReLU")
        elif self.use_enhanced_activation == "mish_only":
            # 只使用浅层Mish组合
            self.shallow_activation = Mish()
            # 深层仍使用标准ReLU
            self.deep_activation_L_64 = nn.ReLU()
            self.deep_activation_S_64 = nn.ReLU()
            self.deep_activation_T_64 = nn.ReLU()
            self.deep_activation_L_128 = nn.ReLU()
            self.deep_activation_S_128 = nn.ReLU()
            self.deep_activation_T_128 = nn.ReLU()
            self.ac_activation_128 = nn.ReLU()
            print("⚡ 激活函数配置: 浅层Mish + 深层ReLU")
        else:
            # 标准ReLU激活函数
            self.shallow_activation = nn.ReLU()
            self.deep_activation_L_64 = nn.ReLU()
            self.deep_activation_S_64 = nn.ReLU()
            self.deep_activation_T_64 = nn.ReLU()
            self.deep_activation_L_128 = nn.ReLU()
            self.deep_activation_S_128 = nn.ReLU()
            self.deep_activation_T_128 = nn.ReLU()
            self.ac_activation_128 = nn.ReLU()
            print("🔧 激活函数配置: 标准ReLU")

        self.bn1_L = nn.BatchNorm2d(64)
        self.bn1_S = nn.BatchNorm2d(64)
        self.bn1_T = nn.BatchNorm2d(64)
        self.maxpool = nn.MaxPool2d(kernel_size=5, stride=2, padding=2)

        self.AC1_conv1_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv1_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv1_T = TemporalShift(nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2,
                                     n_div=8)
        self.AC1_bn1_L = nn.BatchNorm2d(128)
        self.AC1_bn1_S = nn.BatchNorm2d(128)
        self.AC1_bn1_T = nn.BatchNorm2d(128)

        self.AC1_conv2_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv2_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC1_conv2_T = TemporalShift(nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2,
                                     n_div=8)
        self.AC1_bn2_L = nn.BatchNorm2d(128)
        self.AC1_bn2_S = nn.BatchNorm2d(128)
        self.AC1_bn2_T = nn.BatchNorm2d(128)
        self.AC1_pool = nn.AdaptiveAvgPool2d(1)
        # AC1全连接层 - 根据融合方式调整输入维度
        ac1_input_dim = 128 if use_trimodal_fusion else 384
        self.AC1_fc = nn.Linear(in_features=ac1_input_dim, out_features=out_channels)

        # conv2层根据配置选择不同的卷积类型
        if self.use_wtprfaconv_lite:
            # 使用轻量化WTPRFAConv进行第二层处理
            self.conv2_L = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv2_S = WTPRFAConv_Lite(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            # 对于时序分支，仍然使用TemporalShift包装的标准卷积
            self.conv2_T = TemporalShift(nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2,
                                         n_div=8)
        elif self.use_wtprfaconv:
            # 使用WTPRFAConv进行第二层小波变换风车形感受野注意力卷积，进一步增强特征提取
            self.conv2_L = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            self.conv2_S = WTPRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3, pinwheel_kernel_size=3)
            # 对于时序分支，仍然使用TemporalShift包装的标准卷积，因为WTPRFAConv与TemporalShift的兼容性需要进一步验证
            self.conv2_T = TemporalShift(nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2,
                                         n_div=8)
        elif self.use_wtrfaconv:
            # 使用WTRFAConv进行第二层小波变换感受野注意力卷积，进一步增强频域和空间特征提取
            self.conv2_L = WTRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            self.conv2_S = WTRFAConv(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type, rfa_kernel_size=3)
            # 对于时序分支，仍然使用TemporalShift包装的标准卷积，因为WTRFAConv与TemporalShift的兼容性需要进一步验证
            self.conv2_T = TemporalShift(nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2,
                                         n_div=8)
        elif self.use_wtconv:
            # 使用WTConv进行第二层小波变换卷积，进一步增强频域特征提取
            self.conv2_L = WTConv2d(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type)
            self.conv2_S = WTConv2d(in_channels=64, out_channels=64, kernel_size=3, wt_levels=self.wt_levels, wt_type=self.wt_type)
            # 对于时序分支，仍然使用TemporalShift包装的标准卷积，因为WTConv与TemporalShift的兼容性需要进一步验证
            self.conv2_T = TemporalShift(nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2,
                                         n_div=8)
        elif self.use_pinwheel_conv:
            self.conv2_L = Pinwheel_shaped_Convolution(c1=64, c2=64, k=3, s=1)
            self.conv2_S = Pinwheel_shaped_Convolution(c1=64, c2=64, k=3, s=1)
            # 对于时序分支，仍然使用TemporalShift包装的标准卷积，因为风车形卷积与TemporalShift的兼容性需要进一步验证
            self.conv2_T = TemporalShift(nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2,
                                         n_div=8)
        else:
            self.conv2_L = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
            self.conv2_S = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
            self.conv2_T = TemporalShift(nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2,
                                         n_div=8)
        self.bn2_L = nn.BatchNorm2d(64)
        self.bn2_S = nn.BatchNorm2d(64)
        self.bn2_T = nn.BatchNorm2d(64)

        self.conv3_L = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
        self.conv3_S = nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1)
        self.conv3_T = TemporalShift(nn.Conv2d(64, out_channels=64, kernel_size=3, stride=1, padding=1), n_segment=2,
                                     n_div=8)
        self.bn3_L = nn.BatchNorm2d(64)
        self.bn3_S = nn.BatchNorm2d(64)
        self.bn3_T = nn.BatchNorm2d(64)

        self.avgpool = nn.AvgPool2d(kernel_size=3, stride=2, padding=1)

        self.AC2_conv1_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv1_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv1_T = TemporalShift(nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2,
                                     n_div=8)
        self.AC2_bn1_L = nn.BatchNorm2d(128)
        self.AC2_bn1_S = nn.BatchNorm2d(128)
        self.AC2_bn1_T = nn.BatchNorm2d(128)

        self.AC2_conv2_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv2_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.AC2_conv2_T = TemporalShift(nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2,
                                     n_div=8)
        self.AC2_bn2_L = nn.BatchNorm2d(128)
        self.AC2_bn2_S = nn.BatchNorm2d(128)
        self.AC2_bn2_T = nn.BatchNorm2d(128)
        self.AC2_pool = nn.AdaptiveAvgPool2d(1)
        # AC2全连接层 - 根据融合方式调整输入维度
        ac2_input_dim = 128 if use_trimodal_fusion else 384
        self.AC2_fc = nn.Linear(in_features=ac2_input_dim, out_features=out_channels)

        self.all_avgpool = nn.AdaptiveAvgPool2d(1)
        self.conv4_L = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv4_S = nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv4_T = TemporalShift(nn.Conv2d(64, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2,
                                     n_div=8)
        self.bn4_L = nn.BatchNorm2d(128)
        self.bn4_S = nn.BatchNorm2d(128)
        self.bn4_T = nn.BatchNorm2d(128)

        self.conv5_L = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv5_S = nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1)
        self.conv5_T = TemporalShift(nn.Conv2d(128, out_channels=128, kernel_size=3, stride=1, padding=1), n_segment=2,
                                     n_div=8)
        self.bn5_L = nn.BatchNorm2d(128)
        self.bn5_S = nn.BatchNorm2d(128)
        self.bn5_T = nn.BatchNorm2d(128)

        # 最终全连接层 - 根据融合方式调整输入维度
        final_input_dim = 128 if use_trimodal_fusion else 384
        self.fc2 = nn.Linear(in_features=final_input_dim, out_features=out_channels)

        # 注意力模块选择 - ECA或GRSA
        if self.use_grsa:
            # 使用GRSA分组残差自注意力机制
            self.ECA1 = GRSA_ChannelAdapter(64, num_groups=2, reduction=4)
            self.ECA2 = GRSA_ChannelAdapter(64, num_groups=2, reduction=4)
            self.ECA3 = GRSA_ChannelAdapter(64, num_groups=2, reduction=4)
            self.ECA4 = GRSA_ChannelAdapter(128, num_groups=4, reduction=4)
            self.ECA5 = GRSA_ChannelAdapter(128, num_groups=4, reduction=4)

            self.AC1_ECA1 = GRSA_ChannelAdapter(128, num_groups=4, reduction=4)
            self.AC1_ECA2 = GRSA_ChannelAdapter(128, num_groups=4, reduction=4)
            self.AC2_ECA1 = GRSA_ChannelAdapter(128, num_groups=4, reduction=4)
            self.AC2_ECA2 = GRSA_ChannelAdapter(128, num_groups=4, reduction=4)
            print("🔥 使用GRSA分组残差自注意力机制 - 高效空间-通道联合建模")
        else:
            # 使用标准ECA注意力模块
            self.ECA1 = eca_layer_2d_v2(64)
            self.ECA2 = eca_layer_2d_v2(64)
            self.ECA3 = eca_layer_2d_v2(64)
            self.ECA4 = eca_layer_2d_v2(128)
            self.ECA5 = eca_layer_2d_v2(128)

            self.AC1_ECA1 = eca_layer_2d_v2(128)
            self.AC1_ECA2 = eca_layer_2d_v2(128)
            self.AC2_ECA1 = eca_layer_2d_v2(128)
            self.AC2_ECA2 = eca_layer_2d_v2(128)

        # LEGM模块配置 - 仅在位置1使用，用于增强初期特征提取（低层纹理和边缘特征）
        if self.use_legm:
            # 使用标准LEGM
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            self.legm1_L = LEGM(network_depth=6, dim=64, depth=2, num_heads=4,
                               window_size=8, attn_ratio=0.5, attn_loc='last', conv_type='DWConv')
            self.legm1_S = LEGM(network_depth=6, dim=64, depth=2, num_heads=4,
                               window_size=8, attn_ratio=0.5, attn_loc='last', conv_type='DWConv')

        self.amp_factor = amp_factor

        self.consensus = ConsensusModule("avg")

        self.dropout = nn.Dropout(0.2)

        # 初始化三模态特征融合模块
        if self.use_trimodal_fusion:
            # 导入融合模块
            try:
                from PRO_CODE.TriModal_Feature_Fusion import TriModalFeatureFusion
                print(f"🔥 启用三模态特征融合: {trimodal_fusion_type}")

                # 为三个融合点创建融合模块
                self.fusion_AC1 = TriModalFeatureFusion(
                    feature_dim=128,
                    fusion_type=trimodal_fusion_type,
                    num_heads=trimodal_num_heads
                )
                self.fusion_AC2 = TriModalFeatureFusion(
                    feature_dim=128,
                    fusion_type=trimodal_fusion_type,
                    num_heads=trimodal_num_heads
                )
                self.fusion_final = TriModalFeatureFusion(
                    feature_dim=128,
                    fusion_type=trimodal_fusion_type,
                    num_heads=trimodal_num_heads
                )
                print("✅ 三模态融合模块初始化完成")
            except ImportError as e:
                print(f"❌ 无法导入三模态融合模块: {e}")
                print("🔄 回退到传统拼接方式")
                self.use_trimodal_fusion = False
        else:
            print("📝 使用传统特征拼接方式")

    def forward(self, input):
        x1 = input[:, 2:18, :, :]
        x1_onset = input[:, 18:34, :, :]
        x2 = input[:, 0, :, :].unsqueeze(dim=1)
        x2_onset = input[:, 1, :, :].unsqueeze(dim=1)
        x3 = input[:, 34:, :, :]

        bsz = x1.shape[0]

        x3 = torch.reshape(x3, (bsz * 2, 2, 48, 48))

        # 根据输入设备创建相应的零张量
        device = x3.device
        x3_onset = torch.zeros(bsz * 2, 2, 48, 48, device=device)

        motion_x1_onset = self.Aug_Encoder_L(x1_onset)
        motion_x1 = self.Aug_Encoder_L(x1)
        x1 = self.Aug_Manipulator_L(motion_x1_onset, motion_x1, self.amp_factor)
        motion_x2_onset = self.Aug_Encoder_S(x2_onset)
        motion_x2 = self.Aug_Encoder_S(x2)
        x2 = self.Aug_Manipulator_S(motion_x2_onset, motion_x2, self.amp_factor)
        motion_x3_onset = self.Aug_Encoder_T(x3_onset)
        motion_x3 = self.Aug_Encoder_T(x3)
        x3 = self.Aug_Manipulator_T(motion_x3_onset, motion_x3, self.amp_factor)

        # 根据配置选择不同的卷积处理方式
        if self.use_wtprfaconv_lite or self.use_wtprfaconv or self.use_wtrfaconv or self.use_wtconv:
            # WTPRFAConv_Lite、WTPRFAConv、WTRFAConv和WTConv都需要先调整通道数
            x1 = self.conv1_L_pre(x1)
            x1 = self.conv1_L(x1)
            x1 = self.bn1_L(x1)
            x1 = self.shallow_activation(x1)  # 浅层激活函数：Mish或ReLU
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x1 = self.legm1_L(x1)
            else:
                x1 = self.ECA1(x1)
            x1 = self.maxpool(x1)

            x2 = self.conv1_S_pre(x2)
            x2 = self.conv1_S(x2)
            x2 = self.bn1_S(x2)
            x2 = self.shallow_activation(x2)  # 浅层激活函数：Mish或ReLU
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x2 = self.legm1_S(x2)
            x2 = self.maxpool(x2)

            x3 = self.conv1_T_pre(x3)
            x3 = self.conv1_T(x3)
            x3 = self.bn1_T(x3)
            x3 = self.shallow_activation(x3)  # 浅层激活函数：Mish或ReLU
            x3 = self.maxpool(x3)
        else:
            # 标准卷积或风车形卷积
            x1 = self.conv1_L(x1)
            x1 = self.bn1_L(x1)
            x1 = self.shallow_activation(x1)  # 浅层激活函数：Mish或ReLU
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x1 = self.legm1_L(x1)
            else:
                x1 = self.ECA1(x1)
            x1 = self.maxpool(x1)

            x2 = self.conv1_S(x2)
            x2 = self.bn1_S(x2)
            x2 = self.shallow_activation(x2)  # 浅层激活函数：Mish或ReLU
            # LEGM位置1：第一个卷积层后，增强初期特征提取
            if self.use_legm:
                x2 = self.legm1_S(x2)
            x2 = self.maxpool(x2)

            x3 = self.conv1_T(x3)
            x3 = self.bn1_T(x3)
            x3 = self.shallow_activation(x3)  # 浅层激活函数：Mish或ReLU
            x3 = self.maxpool(x3)

        AC1_x1 = self.AC1_conv1_L(x1)
        AC1_x1 = self.AC1_bn1_L(AC1_x1)
        AC1_x1 = self.ac_activation_128(AC1_x1)  # 深层激活函数：FReLU或ReLU
        AC1_x1 = self.AC1_ECA1(AC1_x1)
        AC1_x1 = self.AC1_conv2_L(AC1_x1)
        AC1_x1 = self.AC1_bn2_L(AC1_x1)
        AC1_x1 = self.ac_activation_128(AC1_x1)  # 深层激活函数：FReLU或ReLU
        AC1_x1 = self.AC1_ECA2(AC1_x1)
        AC1_x1 = self.AC1_pool(AC1_x1)
        AC1_x1_all = AC1_x1.view(AC1_x1.size(0), -1)

        AC1_x2 = self.AC1_conv1_S(x2)
        AC1_x2 = self.AC1_bn1_S(AC1_x2)
        AC1_x2 = self.ac_activation_128(AC1_x2)  # 深层激活函数：FReLU或ReLU
        AC1_x2 = self.AC1_conv2_S(AC1_x2)
        AC1_x2 = self.AC1_bn2_S(AC1_x2)
        AC1_x2 = self.ac_activation_128(AC1_x2)  # 深层激活函数：FReLU或ReLU
        AC1_x2 = self.AC1_pool(AC1_x2)
        AC1_x2_all = AC1_x2.view(AC1_x2.size(0), -1)

        AC1_x3 = self.AC1_conv1_T(x3)
        AC1_x3 = self.AC1_bn1_T(AC1_x3)
        AC1_x3 = self.ac_activation_128(AC1_x3)  # 深层激活函数：FReLU或ReLU
        AC1_x3 = self.AC1_conv2_T(AC1_x3)
        AC1_x3 = self.AC1_bn2_T(AC1_x3)
        AC1_x3 = self.ac_activation_128(AC1_x3)  # 深层激活函数：FReLU或ReLU
        AC1_x3 = self.AC1_pool(AC1_x3)
        AC1_x3_all = AC1_x3.view(AC1_x3.size(0), -1)

        AC1_x3_all = AC1_x3_all.view((-1, 2) + AC1_x3_all.size()[1:])
        AC1_x3_all = self.consensus(AC1_x3_all)
        AC1_x3_all = AC1_x3_all.squeeze(1)

        # AC1特征融合 - 使用先进融合或传统拼接
        if self.use_trimodal_fusion:
            AC1_feature = self.fusion_AC1(AC1_x1_all, AC1_x2_all, AC1_x3_all)
        else:
            AC1_feature = torch.cat((AC1_x1_all, AC1_x2_all, AC1_x3_all), 1)

        AC1_x_all = self.dropout(AC1_feature)
        AC1_x_all = self.AC1_fc(AC1_x_all)


        x1 = self.conv2_L(x1)
        x1 = self.bn2_L(x1)
        x1 = self.deep_activation_L_64(x1)  # 深层激活函数：FReLU(64)或ReLU
        x1 = self.ECA2(x1)
        x1 = self.conv3_L(x1)
        x1 = self.bn3_L(x1)
        x1 = self.deep_activation_L_64(x1)  # 深层激活函数：FReLU(64)或ReLU
        x1 = self.ECA3(x1)
        x1 = self.avgpool(x1)

        x2 = self.conv2_S(x2)
        x2 = self.bn2_S(x2)
        x2 = self.deep_activation_S_64(x2)  # 深层激活函数：FReLU(64)或ReLU
        x2 = self.ECA2(x2)
        x2 = self.conv3_S(x2)
        x2 = self.bn3_S(x2)
        x2 = self.deep_activation_S_64(x2)  # 深层激活函数：FReLU(64)或ReLU
        x2 = self.avgpool(x2)

        x3 = self.conv2_T(x3)
        x3 = self.bn2_T(x3)
        x3 = self.deep_activation_T_64(x3)  # 深层激活函数：FReLU(64)或ReLU
        x3 = self.conv3_T(x3)
        x3 = self.bn3_T(x3)
        x3 = self.deep_activation_T_64(x3)  # 深层激活函数：FReLU(64)或ReLU
        x3 = self.avgpool(x3)

        AC2_x1 = self.AC2_conv1_L(x1)
        AC2_x1 = self.AC2_bn1_L(AC2_x1)
        AC2_x1 = self.ac_activation_128(AC2_x1)  # 深层激活函数：FReLU或ReLU
        AC2_x1 = self.AC2_ECA1(AC2_x1)
        AC2_x1 = self.AC2_conv2_L(AC2_x1)
        AC2_x1 = self.AC2_bn2_L(AC2_x1)
        AC2_x1 = self.ac_activation_128(AC2_x1)  # 深层激活函数：FReLU或ReLU
        AC2_x1 = self.AC2_ECA2(AC2_x1)
        AC2_x1 = self.AC2_pool(AC2_x1)
        AC2_x1_all = AC2_x1.view(AC2_x1.size(0), -1)

        AC2_x2 = self.AC2_conv1_S(x2)
        AC2_x2 = self.AC2_bn1_S(AC2_x2)
        AC2_x2 = self.ac_activation_128(AC2_x2)  # 深层激活函数：FReLU或ReLU
        AC2_x2 = self.AC2_conv2_S(AC2_x2)
        AC2_x2 = self.AC2_bn2_S(AC2_x2)
        AC2_x2 = self.ac_activation_128(AC2_x2)  # 深层激活函数：FReLU或ReLU
        AC2_x2 = self.AC2_pool(AC2_x2)
        AC2_x2_all = AC2_x2.view(AC2_x2.size(0), -1)

        AC2_x3 = self.AC2_conv1_T(x3)
        AC2_x3 = self.AC2_bn1_T(AC2_x3)
        AC2_x3 = self.ac_activation_128(AC2_x3)  # 深层激活函数：FReLU或ReLU
        AC2_x3 = self.AC2_conv2_T(AC2_x3)
        AC2_x3 = self.AC2_bn2_T(AC2_x3)
        AC2_x3 = self.ac_activation_128(AC2_x3)  # 深层激活函数：FReLU或ReLU
        AC2_x3 = self.AC2_pool(AC2_x3)
        AC2_x3_all = AC2_x3.view(AC2_x3.size(0), -1)

        AC2_x3_all = AC2_x3_all.view((-1, 2) + AC2_x3_all.size()[1:])
        AC2_x3_all = self.consensus(AC2_x3_all)
        AC2_x3_all = AC2_x3_all.squeeze(1)

        # AC2特征融合 - 使用先进融合或传统拼接
        if self.use_trimodal_fusion:
            AC2_feature = self.fusion_AC2(AC2_x1_all, AC2_x2_all, AC2_x3_all)
        else:
            AC2_feature = torch.cat((AC2_x1_all, AC2_x2_all, AC2_x3_all), 1)

        AC2_x_all = self.dropout(AC2_feature)
        AC2_x_all = self.AC2_fc(AC2_x_all)


        x1 = self.conv4_L(x1)
        x1 = self.bn4_L(x1)
        x1 = self.deep_activation_L_128(x1)  # 深层激活函数：FReLU(128)或ReLU
        x1 = self.ECA4(x1)
        x1 = self.conv5_L(x1)
        x1 = self.bn5_L(x1)
        x1 = self.deep_activation_L_128(x1)  # 深层激活函数：FReLU(128)或ReLU
        x1 = self.ECA5(x1)

        x1 = self.all_avgpool(x1)
        x1_all = x1.view(x1.size(0), -1)

        x2 = self.conv4_S(x2)
        x2 = self.bn4_S(x2)
        x2 = self.deep_activation_S_128(x2)  # 深层激活函数：FReLU(128)或ReLU
        x2 = self.conv5_S(x2)
        x2 = self.bn5_S(x2)
        x2 = self.deep_activation_S_128(x2)  # 深层激活函数：FReLU(128)或ReLU

        x2 = self.all_avgpool(x2)
        x2_all = x2.view(x2.size(0), -1)

        x3 = self.conv4_T(x3)
        x3 = self.bn4_T(x3)
        x3 = self.deep_activation_T_128(x3)  # 深层激活函数：FReLU(128)或ReLU
        x3 = self.conv5_T(x3)
        x3 = self.bn5_T(x3)
        x3 = self.deep_activation_T_128(x3)  # 深层激活函数：FReLU(128)或ReLU
        x3 = self.all_avgpool(x3)
        x3_all = x3.view(x3.size(0), -1)

        x3_all = x3_all.view((-1, 2) + x3_all.size()[1:])

        x3_all = self.consensus(x3_all)
        x3_all = x3_all.squeeze(1)

        # 最终特征融合 - 使用先进融合或传统拼接
        if self.use_trimodal_fusion:
            final_feature = self.fusion_final(x1_all, x2_all, x3_all)
        else:
            final_feature = torch.cat((x1_all, x2_all, x3_all), 1)

        x_all = self.dropout(final_feature)
        x_all = self.fc2(x_all)
        return x_all, AC1_x_all, AC2_x_all, final_feature, AC1_feature, AC2_feature


def get_model(model_name, class_num, alpha, use_pinwheel_conv=False, use_wtconv=False,
              use_wtrfaconv=False, use_wtprfaconv=False, use_wtprfaconv_lite=False,
              use_legm=False, use_grsa=False, use_eca_enhanced=False, wt_type='db4', wt_levels=1,
              use_trimodal_fusion=False, trimodal_fusion_type='ensemble', trimodal_num_heads=8,
              use_enhanced_activation=False):
    if model_name == "SKD_TSTSAN":
        return SKD_TSTSAN(out_channels=class_num, amp_factor=alpha,
                         use_pinwheel_conv=use_pinwheel_conv, use_wtconv=use_wtconv,
                         use_wtrfaconv=use_wtrfaconv, use_wtprfaconv=use_wtprfaconv,
                         use_wtprfaconv_lite=use_wtprfaconv_lite, use_legm=use_legm,
                         use_grsa=use_grsa, wt_type=wt_type, wt_levels=wt_levels,
                         use_trimodal_fusion=use_trimodal_fusion,
                         trimodal_fusion_type=trimodal_fusion_type,
                         trimodal_num_heads=trimodal_num_heads,
                         use_enhanced_activation=use_enhanced_activation)


def get_model_by_conv_type(model_name, class_num, alpha, conv_type=1, use_legm=False,
                          use_grsa=False, wt_type='db4', wt_levels=1,
                          use_trimodal_fusion=False, trimodal_fusion_type='ensemble',
                          trimodal_num_heads=8, use_enhanced_activation=False):
    """
    通过卷积类型数字选择创建模型

    Args:
        model_name: 模型名称
        class_num: 分类数量
        alpha: 放大因子
        conv_type: 卷积类型选择
            1 - 标准卷积
            2 - 风车形卷积
            3 - 小波变换卷积
            4 - 小波变换感受野注意力卷积
            5 - 小波变换风车形感受野注意力卷积 (原版，参数多)
            6 - 小波变换风车形感受野注意力卷积 (轻量化版本)
        use_legm: 是否使用标准LEGM模块进行局部-全局特征增强
        wt_type: 小波类型 (仅对小波相关卷积有效)
        wt_levels: 小波变换层数 (仅对小波相关卷积有效)
        use_trimodal_fusion: 是否启用三模态特征融合
        trimodal_fusion_type: 三模态融合类型 ('attention', 'gated', 'high_order', 'ensemble')
        trimodal_num_heads: 多头注意力的头数 (仅对attention类型有效)

    Returns:
        模型实例
    """
    conv_configs = {
        1: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        2: {"use_pinwheel_conv": True, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        3: {"use_pinwheel_conv": False, "use_wtconv": True, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        4: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": True, "use_wtprfaconv": False, "use_wtprfaconv_lite": False},
        5: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": True, "use_wtprfaconv_lite": False},
        6: {"use_pinwheel_conv": False, "use_wtconv": False, "use_wtrfaconv": False, "use_wtprfaconv": False, "use_wtprfaconv_lite": True},
    }

    if conv_type not in conv_configs:
        raise ValueError(f"无效的卷积类型: {conv_type}. 请选择1-6之间的数字。")

    config = conv_configs[conv_type]

    return get_model(
        model_name=model_name,
        class_num=class_num,
        alpha=alpha,
        use_legm=use_legm,
        use_grsa=use_grsa,
        wt_type=wt_type,
        wt_levels=wt_levels,
        use_trimodal_fusion=use_trimodal_fusion,
        trimodal_fusion_type=trimodal_fusion_type,
        trimodal_num_heads=trimodal_num_heads,
        use_enhanced_activation=use_enhanced_activation,
        **config
    )