日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (5分类)[0m
[1;96m🎯 类别权重: ['0.2328', '0.2979', '0.1182', '0.2759', '0.0752'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: sub17
========================================
组别: 大样本组(>12个样本)
正在加载训练数据...
正在加载测试数据...

sub17测试标签: [4, 4, 4, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3]
创建受试者目录: sub17
创建TensorBoard日志目录: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv6/sub17/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv6/sub17/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换风车形感受野注意力卷积 (轻量化) 模型...[0m
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
=> Using fold div: 8
[1;92m✅ 启用小波变换风车形感受野注意力卷积功能(轻量化):[0m
  [96m- 融合三种先进技术的轻量化版本[0m ⚡
  [96m- 大幅减少参数数量，保持核心功能[0m 🎯
  [1;95m- 推荐选择，性能与效率的最佳平衡[0m ⭐

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda
  - 批次大小: 32
加载预训练模型...

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 770 个epoch的精彩旅程即将开始！[0m

[1m[1;96m📅 Epoch 1/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.4509[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.2647[0m | [1m最佳准确率:[0m [1;93m0.0000[0m
保存最佳模型,准确率: 0.2647
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv6/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv6/sub17/sub17.pth

[1m[1;96m📅 Epoch 2/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.6830[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2647[0m | [1m最佳准确率:[0m [1;93m0.2647[0m
保存最佳模型,准确率: 0.2647
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv6/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv6/sub17/sub17.pth

[1m[1;96m📅 Epoch 3/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.7740[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.3529[0m | [1m最佳准确率:[0m [1;93m0.2647[0m
保存最佳模型,准确率: 0.3529
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv6/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv6/sub17/sub17.pth

[1m[1;96m📅 Epoch 4/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.8477[0m

开始验证...
📊 [1m验证集准确率:[0m [1;92m0.7941[0m | [1m最佳准确率:[0m [1;93m0.3529[0m
保存最佳模型,准确率: 0.7941
模型保存路径: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv6/sub17/sub17.pth
模型保存成功: ./Experiment_for_recognize/SKD_TSTSAN_CASME2_class5_full_conv6/sub17/sub17.pth

[1m[1;96m📅 Epoch 5/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9226[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5588[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 6/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9373[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6471[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 7/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9570[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7059[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 8/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9644[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5882[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 9/770[0m
[1;96m---------------[0m
[1;92m🎯 训练集准确率: 0.9509[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4706[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 10/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9742[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7353[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 11/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9853[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4118[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 12/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9988[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 13/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9840[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.3824[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 14/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9484[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6471[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 15/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9828[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7647[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 16/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9902[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6176[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 17/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7059[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 18/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9975[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7647[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 19/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9914[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7647[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 20/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9926[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7059[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 21/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6765[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 22/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9840[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7353[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 23/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9865[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6471[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 24/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9717[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7059[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 25/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9767[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7059[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 26/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9951[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5882[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 27/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9951[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5882[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 28/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9988[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5588[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 29/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9791[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7059[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 30/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9779[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6176[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 31/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 32/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4706[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 33/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5294[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 34/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4706[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 35/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 36/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7059[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 37/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9889[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5294[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 38/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9914[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5588[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 39/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9951[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6471[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 40/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9816[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4118[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 41/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9877[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7647[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 42/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9779[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5588[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 43/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9717[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6176[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 44/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9545[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2647[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 45/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9840[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4706[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 46/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9988[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 47/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9988[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5882[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 48/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5588[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 49/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9988[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6471[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 50/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4118[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 51/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5588[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 52/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 1.0000[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4118[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 53/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 54/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9730[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.2647[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 55/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5294[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 56/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9914[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5588[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 57/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9840[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 58/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9926[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6471[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 59/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9877[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5000[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 60/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9877[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.7647[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 61/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9803[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.4706[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 62/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9877[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6471[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 63/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9963[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.6471[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 64/770[0m
[1;96m----------------[0m
[1;92m🎯 训练集准确率: 0.9939[0m

开始验证...
📊 [1m验证集准确率:[0m [1;96m0.5882[0m | [1m最佳准确率:[0m [1;93m0.7941[0m

[1m[1;96m📅 Epoch 65/770[0m
[1;96m----------------[0m
