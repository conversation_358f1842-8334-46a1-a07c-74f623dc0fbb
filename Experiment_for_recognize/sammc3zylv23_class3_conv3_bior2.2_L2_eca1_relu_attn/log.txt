日志系统初始化成功

================================================================================
【训练开始】
================================================================================

【硬件信息】检测到1个GPU
  GPU 0: NVIDIA A800 80GB PCIe (79.3GB)

【单GPU配置】使用单个GPU进行训练
  - 批次大小: 32
  - GPU内存限制: 95%
  - 主设备: cuda
✓ TF32加速已启用
✗ 混合精度训练已禁用
✗ BatchNorm修复已禁用 (使用原始BatchNorm层)

[1m[1;95m📊 损失函数配置[0m
[1;95m----------[0m
[1;92m⚖️ 使用加权Focal Loss (SAMM 3分类)[0m
[1;96m🎯 类别权重: ['0.3309', '0.0956', '0.5735'][0m
开始训练...
根据配置,不使用Visdom可视化

========================================
【当前处理受试者】: 011
========================================
组别: 大样本组(>8个测试样本)
正在加载训练数据...
正在加载测试数据...

011测试标签: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0]
创建受试者目录: 011
创建TensorBoard日志目录: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/logs
TensorBoard写入器创建成功: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/logs

[1m[1;94m🤖 模型初始化[0m
[1;94m---------[0m
[1;96m⚙️ 正在初始化 小波变换卷积 模型...[0m
🌊 小波配置: 类型=bior2.2, 层数=2
🔧 激活函数配置: 标准ReLU
🔥 启用三模态特征融合: attention
✅ 三模态融合模块初始化完成
[1;92m✅ 启用WTConv小波变换卷积功能:[0m
  [1;96m- 小波类型: bior2.2 (层数: 2)[0m 🌊
  [96m- 通过小波变换实现多频响应特征提取[0m 🌊
  [96m- 同时捕获微表情的全局结构和局部细节[0m 🎭
  [96m- 实现非常大的感受野而不会过度参数化[0m 📡
  [96m- 特别适合微表情的频域特征分析[0m 📊
[1;92m🔥 启用三模态特征融合:[0m
  [1;96m多头注意力融合[0m 🧠
  [96m- 建立跨模态注意力关系[0m 🔗
  [96m- 动态关注重要特征[0m 🎯
  [1;96m- 注意力头数: 8[0m 🎯
[1;92m🛑 启用早停机制:[0m
  [1;96m智能训练优化[0m ⚡
  [96m- 监控指标: accuracy[0m 📊
  [96m- 耐心值: 300 epochs[0m ⏳
  [96m- 改善阈值: 0.001[0m 📈
  [96m- 自动保存最佳模型[0m 💾
  [96m- 防止过拟合，提高训练效率[0m 🎯

【BatchNorm修复】已禁用，使用原始BatchNorm层
  - 如果遇到小批次训练问题，可以设置 --use_batchnorm_fix=True 来启用修复

【单GPU配置】模型将在单个GPU上训练
  - 使用设备: cuda:0
  - 批次大小: 32
加载预训练模型...

[1m[1;92m🚀 GPU数据预加载[0m
[1;92m------------[0m
[1;96m📤 正在将所有数据预加载到GPU内存...[0m
[1;96m📊 训练数据大小: 0.14 GB[0m
[1;96m📊 测试数据大小: 0.02 GB[0m
[1;96m📊 总数据大小: 0.17 GB[0m
[1;96m⬆️ 正在上传训练数据到GPU...[0m
[1;96m⬆️ 正在上传测试数据到GPU...[0m
[1;92m✅ 数据预加载到GPU完成！[0m
[1;96m💾 GPU内存使用: GPU0: 0.2GB/79.3GB (0.2%)[0m
[1;92m🎯 ✅ GPU数据预加载完成确认:[0m
[1;96m✅   📦 训练数据集: 437 个样本已在GPU内存[0m
[1;96m✅   📦 测试数据集: 75 个样本已在GPU内存[0m
[1;96m📊   🚀 训练批次数: 14 个批次[0m
[1;96m📊   🚀 测试批次数: 3 个批次[0m
[1;96m🚀   ⚡ 所有数据已驻留在GPU，训练时无需CPU-GPU传输！[0m
[1;96m⚡ GPU预加载模式：数据传输将极大加速！[0m

[1m[1;93m🛑 早停配置[0m
[1;93m--------[0m
[1;96m📊 监控指标: accuracy[0m
[1;96m⏳ 耐心值: 300 epochs[0m
[1;96m📈 最小改善阈值: 0.001[0m
[1;96m🎯 模式: 最大化[0m

[1m[1;96m📊 损失输出配置[0m
[1;96m----------[0m
[1;96m🔍 详细损失信息: 禁用[0m
[1;96m📈 Epoch损失统计: 禁用[0m
[1;96m📉 验证损失统计: 禁用[0m
[1;96m🚨 过拟合监控: 禁用[0m
[1;96m📦 批次损失详情: 禁用[0m
[1;96m❌ Epoch损失统计将显示: 否[0m
[1;96m❌ 验证损失统计将显示: 否[0m
[1;96m❌ 过拟合监控将显示: 否[0m

[1;92m==============[0m
[1m[1;92m🎯 🚀 开始训练征程 🚀 🎯[0m
[1;92m==============[0m

[1m[1;95m🌟 总共 1429 个epoch的精彩旅程即将开始！[0m
[1;92m⚡ 🚀 GPU加速模式已激活！[0m
[1;96m💾   📍 所有训练和测试数据已预加载到GPU内存[0m
[1;96m🚀   ⚡ 训练过程中将享受零延迟数据访问[0m

[1m[1;96m📅 Epoch 1/1429[0m
[1;96m----------------[0m
[1;92m⚡ 🚀 GPU数据访问确认: 数据直接从GPU内存读取，无CPU-GPU传输延迟！[0m
[1;96m📊   📦 当前批次数据形状: torch.Size([32, 38, 48, 48]), 设备: cuda:0[0m

============================================================
[1m[1;95m📈 📊 Epoch 1 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.5492[0m (240/437)
   • 中间层1准确率: [96m0.5263[0m (230/437)
   • 中间层2准确率: [96m0.5309[0m (232/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 1 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.3500[0m (7/20)
🏆 [1m最佳准确率:[0m [1;93m0.0000[0m
📊 [1mUF1:[0m [1;92m0.2593[0m | [1mUAR:[0m [1;92m0.5000[0m
🥇 [1m最佳UF1:[0m [1;93m0.0000[0m | [1m最佳UAR:[0m [1;93m0.0000[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.3500 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.2593 | UAR: 0.5000 | 准确率: 0.3500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth[0m
模型保存成功: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth
[1;96m📈 🎯 accuracy改善: 0.3500 (最佳: 0.3500)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/best_model_011.pth[0m

[1m[1;96m📅 Epoch 2/1429[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 2 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8055[0m (352/437)
   • 中间层1准确率: [96m0.7391[0m (323/437)
   • 中间层2准确率: [96m0.7643[0m (334/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 2 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.5000[0m (10/20)
🏆 [1m最佳准确率:[0m [1;93m0.3500[0m
📊 [1mUF1:[0m [1;92m0.5057[0m | [1mUAR:[0m [1;92m0.6154[0m
🥇 [1m最佳UF1:[0m [1;93m0.2593[0m | [1m最佳UAR:[0m [1;93m0.5000[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.5000 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.5057 | UAR: 0.6154 | 准确率: 0.5000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth[0m
模型保存成功: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth
[1;96m📈 🎯 accuracy改善: 0.5000 (最佳: 0.5000)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/best_model_011.pth[0m

[1m[1;96m📅 Epoch 3/1429[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 3 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8307[0m (363/437)
   • 中间层1准确率: [96m0.7735[0m (338/437)
   • 中间层2准确率: [96m0.8398[0m (367/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 3 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.5500[0m (11/20)
🏆 [1m最佳准确率:[0m [1;93m0.5000[0m
📊 [1mUF1:[0m [1;92m0.5632[0m | [1mUAR:[0m [1;92m0.6209[0m
🥇 [1m最佳UF1:[0m [1;93m0.5057[0m | [1m最佳UAR:[0m [1;93m0.6154[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.5500 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.5632 | UAR: 0.6209 | 准确率: 0.5500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth[0m
模型保存成功: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth
[1;96m📈 🎯 accuracy改善: 0.5500 (最佳: 0.5500)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/best_model_011.pth[0m

[1m[1;96m📅 Epoch 4/1429[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 4 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8764[0m (383/437)
   • 中间层1准确率: [96m0.8558[0m (374/437)
   • 中间层2准确率: [96m0.8810[0m (385/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 4 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.6500[0m (13/20)
🏆 [1m最佳准确率:[0m [1;93m0.5500[0m
📊 [1mUF1:[0m [1;96m0.4063[0m | [1mUAR:[0m [1;96m0.5000[0m
🥇 [1m最佳UF1:[0m [1;93m0.5632[0m | [1m最佳UAR:[0m [1;93m0.6209[0m
============================================================

[1;93m⚠️ ❌ 准确率提升(0.6500)但UF1(0.4063↓)和UAR(0.5000↓)都下降，不保存[0m
[1;96m📈 🎯 accuracy改善: 0.6500 (最佳: 0.6500)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/best_model_011.pth[0m

[1m[1;96m📅 Epoch 5/1429[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 5 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9130[0m (399/437)
   • 中间层1准确率: [96m0.8856[0m (387/437)
   • 中间层2准确率: [96m0.9176[0m (401/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 5 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.8000[0m (16/20)
🏆 [1m最佳准确率:[0m [1;93m0.5500[0m
📊 [1mUF1:[0m [1;92m0.7619[0m | [1mUAR:[0m [1;92m0.7473[0m
🥇 [1m最佳UF1:[0m [1;93m0.5632[0m | [1m最佳UAR:[0m [1;93m0.6209[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.8000 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.7619 | UAR: 0.7473 | 准确率: 0.8000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth[0m
模型保存成功: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth
[1;96m📈 🎯 accuracy改善: 0.8000 (最佳: 0.8000)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/best_model_011.pth[0m

[1m[1;96m📅 Epoch 6/1429[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 6 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9153[0m (400/437)
   • 中间层1准确率: [96m0.9245[0m (404/437)
   • 中间层2准确率: [96m0.9108[0m (398/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 6 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.8000[0m
📊 [1mUF1:[0m [1;96m0.6801[0m | [1mUAR:[0m [1;96m0.6374[0m
🥇 [1m最佳UF1:[0m [1;93m0.7619[0m | [1m最佳UAR:[0m [1;93m0.7473[0m
============================================================

[1;96m🔄 ⏳ 无改善 (1/300) 剩余耐心: 299[0m

[1m[1;96m📅 Epoch 7/1429[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 7 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9359[0m (409/437)
   • 中间层1准确率: [96m0.9268[0m (405/437)
   • 中间层2准确率: [96m0.9474[0m (414/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 7 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.8000[0m
📊 [1mUF1:[0m [1;96m0.6656[0m | [1mUAR:[0m [1;96m0.6374[0m
🥇 [1m最佳UF1:[0m [1;93m0.7619[0m | [1m最佳UAR:[0m [1;93m0.7473[0m
============================================================

[1;96m🔄 ⏳ 无改善 (2/300) 剩余耐心: 298[0m

[1m[1;96m📅 Epoch 8/1429[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 8 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9634[0m (421/437)
   • 中间层1准确率: [96m0.9519[0m (416/437)
   • 中间层2准确率: [96m0.9703[0m (424/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 8 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6500[0m (13/20)
🏆 [1m最佳准确率:[0m [1;93m0.8000[0m
📊 [1mUF1:[0m [1;96m0.3939[0m | [1mUAR:[0m [1;96m0.5000[0m
🥇 [1m最佳UF1:[0m [1;93m0.7619[0m | [1m最佳UAR:[0m [1;93m0.7473[0m
============================================================

[1;96m🔄 ⏳ 无改善 (3/300) 剩余耐心: 297[0m

[1m[1;96m📅 Epoch 9/1429[0m
[1;96m----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 9 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (430/437)
   • 中间层1准确率: [96m0.9817[0m (429/437)
   • 中间层2准确率: [96m0.9840[0m (430/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 9 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6500[0m (13/20)
🏆 [1m最佳准确率:[0m [1;93m0.8000[0m
📊 [1mUF1:[0m [1;96m0.7348[0m | [1mUAR:[0m [1;96m0.5989[0m
🥇 [1m最佳UF1:[0m [1;93m0.7619[0m | [1m最佳UAR:[0m [1;93m0.7473[0m
============================================================

[1;96m🔄 ⏳ 无改善 (4/300) 剩余耐心: 296[0m

[1m[1;96m📅 Epoch 10/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 10 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9680[0m (423/437)
   • 中间层1准确率: [96m0.9657[0m (422/437)
   • 中间层2准确率: [96m0.9657[0m (422/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 10 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.8000[0m
📊 [1mUF1:[0m [1;96m0.7000[0m | [1mUAR:[0m [1;92m0.7692[0m
🥇 [1m最佳UF1:[0m [1;93m0.7619[0m | [1m最佳UAR:[0m [1;93m0.7473[0m
============================================================

[1;96m🔄 ⏳ 无改善 (5/300) 剩余耐心: 295[0m

[1m[1;96m📅 Epoch 11/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 11 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9703[0m (424/437)
   • 中间层1准确率: [96m0.9794[0m (428/437)
   • 中间层2准确率: [96m0.9611[0m (420/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 11 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6000[0m (12/20)
🏆 [1m最佳准确率:[0m [1;93m0.8000[0m
📊 [1mUF1:[0m [1;96m0.6625[0m | [1mUAR:[0m [1;96m0.6264[0m
🥇 [1m最佳UF1:[0m [1;93m0.7619[0m | [1m最佳UAR:[0m [1;93m0.7473[0m
============================================================

[1;96m🔄 ⏳ 无改善 (6/300) 剩余耐心: 294[0m

[1m[1;96m📅 Epoch 12/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 12 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9611[0m (420/437)
   • 中间层1准确率: [96m0.9680[0m (423/437)
   • 中间层2准确率: [96m0.9771[0m (427/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 12 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6000[0m (12/20)
🏆 [1m最佳准确率:[0m [1;93m0.8000[0m
📊 [1mUF1:[0m [1;96m0.6833[0m | [1mUAR:[0m [1;96m0.6264[0m
🥇 [1m最佳UF1:[0m [1;93m0.7619[0m | [1m最佳UAR:[0m [1;93m0.7473[0m
============================================================

[1;96m🔄 ⏳ 无改善 (7/300) 剩余耐心: 293[0m

[1m[1;96m📅 Epoch 13/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 13 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9886[0m (432/437)
   • 中间层1准确率: [96m0.9886[0m (432/437)
   • 中间层2准确率: [96m0.9840[0m (430/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 13 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.8500[0m (17/20)
🏆 [1m最佳准确率:[0m [1;93m0.8000[0m
📊 [1mUF1:[0m [1;92m0.8291[0m | [1mUAR:[0m [1;92m0.8187[0m
🥇 [1m最佳UF1:[0m [1;93m0.7619[0m | [1m最佳UAR:[0m [1;93m0.7473[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.8500 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.8291 | UAR: 0.8187 | 准确率: 0.8500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth[0m
模型保存成功: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth
[1;96m📈 🎯 accuracy改善: 0.8500 (最佳: 0.8500)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/best_model_011.pth[0m

[1m[1;96m📅 Epoch 14/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 14 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9451[0m (413/437)
   • 中间层1准确率: [96m0.9565[0m (418/437)
   • 中间层2准确率: [96m0.9542[0m (417/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 14 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (10/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.4949[0m | [1mUAR:[0m [1;96m0.5824[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (1/300) 剩余耐心: 299[0m

[1m[1;96m📅 Epoch 15/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 15 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.8719[0m (381/437)
   • 中间层1准确率: [96m0.8924[0m (390/437)
   • 中间层2准确率: [96m0.8719[0m (381/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 15 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (10/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.5771[0m | [1mUAR:[0m [1;96m0.4835[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (2/300) 剩余耐心: 298[0m

[1m[1;96m📅 Epoch 16/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 16 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9268[0m (405/437)
   • 中间层1准确率: [96m0.8398[0m (367/437)
   • 中间层2准确率: [96m0.9359[0m (409/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 16 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6500[0m (13/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.6346[0m | [1mUAR:[0m [1;96m0.5989[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (3/300) 剩余耐心: 297[0m

[1m[1;96m📅 Epoch 17/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 17 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9108[0m (398/437)
   • 中间层1准确率: [96m0.8947[0m (391/437)
   • 中间层2准确率: [96m0.9199[0m (402/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 17 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.6508[0m | [1mUAR:[0m [1;96m0.6044[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (4/300) 剩余耐心: 296[0m

[1m[1;96m📅 Epoch 18/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 18 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9451[0m (413/437)
   • 中间层1准确率: [96m0.9474[0m (414/437)
   • 中间层2准确率: [96m0.9588[0m (419/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 18 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8000[0m (16/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.7333[0m | [1mUAR:[0m [1;96m0.7143[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (5/300) 剩余耐心: 295[0m

[1m[1;96m📅 Epoch 19/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 19 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9748[0m (426/437)
   • 中间层1准确率: [96m0.9565[0m (418/437)
   • 中间层2准确率: [96m0.9611[0m (420/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 19 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7500[0m (15/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.7407[0m | [1mUAR:[0m [1;96m0.7088[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (6/300) 剩余耐心: 294[0m

[1m[1;96m📅 Epoch 20/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 20 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9634[0m (421/437)
   • 中间层1准确率: [96m0.9725[0m (425/437)
   • 中间层2准确率: [96m0.9565[0m (418/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 20 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8500[0m (17/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.8291[0m | [1mUAR:[0m [1;96m0.8187[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (7/300) 剩余耐心: 293[0m

[1m[1;96m📅 Epoch 21/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 21 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9542[0m (417/437)
   • 中间层1准确率: [96m0.9314[0m (407/437)
   • 中间层2准确率: [96m0.9588[0m (419/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 21 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.7484[0m | [1mUAR:[0m [1;96m0.7033[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (8/300) 剩余耐心: 292[0m

[1m[1;96m📅 Epoch 22/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 22 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9817[0m (429/437)
   • 中间层1准确率: [96m0.9748[0m (426/437)
   • 中间层2准确率: [96m0.9886[0m (432/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 22 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7500[0m (15/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.7013[0m | [1mUAR:[0m [1;96m0.6758[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (9/300) 剩余耐心: 291[0m

[1m[1;96m📅 Epoch 23/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 23 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9863[0m (431/437)
   • 中间层1准确率: [96m0.9725[0m (425/437)
   • 中间层2准确率: [96m0.9863[0m (431/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 23 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.5312[0m | [1mUAR:[0m [1;96m0.5714[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (10/300) 剩余耐心: 290[0m

[1m[1;96m📅 Epoch 24/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 24 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9817[0m (429/437)
   • 中间层1准确率: [96m0.9863[0m (431/437)
   • 中间层2准确率: [96m0.9863[0m (431/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 24 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (10/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.6015[0m | [1mUAR:[0m [1;96m0.5165[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (11/300) 剩余耐心: 289[0m

[1m[1;96m📅 Epoch 25/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 25 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9886[0m (432/437)
   • 中间层1准确率: [96m0.9954[0m (435/437)
   • 中间层2准确率: [96m0.9886[0m (432/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 25 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8000[0m (16/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.7971[0m | [1mUAR:[0m [1;96m0.7802[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (12/300) 剩余耐心: 288[0m

[1m[1;96m📅 Epoch 26/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 26 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9931[0m (434/437)
   • 中间层1准确率: [96m0.9954[0m (435/437)
   • 中间层2准确率: [96m0.9977[0m (436/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 26 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8500[0m (17/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;92m0.8462[0m | [1mUAR:[0m [1;96m0.8187[0m
🥇 [1m最佳UF1:[0m [1;93m0.8291[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;92m💾 保存最佳模型，相同准确率下更高UF1+UAR综合评分: 0.8324[0m
[1;96m📊 UF1: 0.8462 | UAR: 0.8187 | 准确率: 0.8500[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth[0m
模型保存成功: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth
[1;96m🔄 ⏳ 无改善 (13/300) 剩余耐心: 287[0m

[1m[1;96m📅 Epoch 27/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 27 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9977[0m (436/437)
   • 中间层1准确率: [96m0.9977[0m (436/437)
   • 中间层2准确率: [96m0.9886[0m (432/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 27 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8500[0m (17/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.8462[0m | [1mUAR:[0m [1;96m0.8187[0m
🥇 [1m最佳UF1:[0m [1;93m0.8462[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (14/300) 剩余耐心: 286[0m

[1m[1;96m📅 Epoch 28/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 28 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (430/437)
   • 中间层1准确率: [96m0.9954[0m (435/437)
   • 中间层2准确率: [96m0.9794[0m (428/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 28 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6500[0m (13/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.7246[0m | [1mUAR:[0m [1;96m0.6319[0m
🥇 [1m最佳UF1:[0m [1;93m0.8462[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (15/300) 剩余耐心: 285[0m

[1m[1;96m📅 Epoch 29/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 29 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9588[0m (419/437)
   • 中间层1准确率: [96m0.9565[0m (418/437)
   • 中间层2准确率: [96m0.9588[0m (419/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 29 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6500[0m (13/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.5250[0m | [1mUAR:[0m [1;96m0.5330[0m
🥇 [1m最佳UF1:[0m [1;93m0.8462[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (16/300) 剩余耐心: 284[0m

[1m[1;96m📅 Epoch 30/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 30 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9130[0m (399/437)
   • 中间层1准确率: [96m0.8902[0m (389/437)
   • 中间层2准确率: [96m0.9291[0m (406/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 30 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.6970[0m | [1mUAR:[0m [1;96m0.7363[0m
🥇 [1m最佳UF1:[0m [1;93m0.8462[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (17/300) 剩余耐心: 283[0m

[1m[1;96m📅 Epoch 31/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 31 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9062[0m (396/437)
   • 中间层1准确率: [96m0.9199[0m (402/437)
   • 中间层2准确率: [96m0.9039[0m (395/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 31 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6000[0m (12/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.4138[0m | [1mUAR:[0m [1;96m0.4615[0m
🥇 [1m最佳UF1:[0m [1;93m0.8462[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (18/300) 剩余耐心: 282[0m

[1m[1;96m📅 Epoch 32/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 32 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9542[0m (417/437)
   • 中间层1准确率: [96m0.9268[0m (405/437)
   • 中间层2准确率: [96m0.9474[0m (414/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 32 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8500[0m (17/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.8279[0m | [1mUAR:[0m [1;96m0.7857[0m
🥇 [1m最佳UF1:[0m [1;93m0.8462[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (19/300) 剩余耐心: 281[0m

[1m[1;96m📅 Epoch 33/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 33 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9565[0m (418/437)
   • 中间层1准确率: [96m0.9542[0m (417/437)
   • 中间层2准确率: [96m0.9725[0m (425/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 33 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8500[0m (17/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.8279[0m | [1mUAR:[0m [1;96m0.7857[0m
🥇 [1m最佳UF1:[0m [1;93m0.8462[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (20/300) 剩余耐心: 280[0m

[1m[1;96m📅 Epoch 34/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 34 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9886[0m (432/437)
   • 中间层1准确率: [96m0.9817[0m (429/437)
   • 中间层2准确率: [96m0.9954[0m (435/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 34 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7500[0m (15/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;96m0.7308[0m | [1mUAR:[0m [1;96m0.7088[0m
🥇 [1m最佳UF1:[0m [1;93m0.8462[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;96m🔄 ⏳ 无改善 (21/300) 剩余耐心: 279[0m

[1m[1;96m📅 Epoch 35/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 35 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9863[0m (431/437)
   • 中间层1准确率: [96m0.9817[0m (429/437)
   • 中间层2准确率: [96m0.9840[0m (430/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 35 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;92m0.9000[0m (18/20)
🏆 [1m最佳准确率:[0m [1;93m0.8500[0m
📊 [1mUF1:[0m [1;92m0.8981[0m | [1mUAR:[0m [1;92m0.8571[0m
🥇 [1m最佳UF1:[0m [1;93m0.8462[0m | [1m最佳UAR:[0m [1;93m0.8187[0m
============================================================

[1;92m💾 保存最佳模型，更高准确率: 0.9000 (UF1和UAR都提升)[0m
[1;96m📊 UF1: 0.8981 | UAR: 0.8571 | 准确率: 0.9000[0m
[1;96m📁 模型保存路径: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth[0m
模型保存成功: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/011.pth
[1;96m📈 🎯 accuracy改善: 0.9000 (最佳: 0.9000)[0m
[1;96m✅ 💾 最佳模型已保存: ./Experiment_for_recognize/sammc3zylv23_class3_conv3_bior2.2_L2_eca1_relu_attn/011/best_model_011.pth[0m

[1m[1;96m📅 Epoch 36/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 36 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 36 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8000[0m (16/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7483[0m | [1mUAR:[0m [1;96m0.7143[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (1/300) 剩余耐心: 299[0m

[1m[1;96m📅 Epoch 37/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 37 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9977[0m (436/437)
   • 中间层1准确率: [96m0.9977[0m (436/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 37 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8500[0m (17/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.8451[0m | [1mUAR:[0m [1;96m0.7857[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (2/300) 剩余耐心: 298[0m

[1m[1;96m📅 Epoch 38/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 38 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 38 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.9000[0m (18/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.8981[0m | [1mUAR:[0m [1;96m0.8571[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (3/300) 剩余耐心: 297[0m

[1m[1;96m📅 Epoch 39/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 39 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9954[0m (435/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m0.9977[0m (436/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 39 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8000[0m (16/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.8246[0m | [1mUAR:[0m [1;96m0.7802[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (4/300) 剩余耐心: 296[0m

[1m[1;96m📅 Epoch 40/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 40 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9954[0m (435/437)
   • 中间层1准确率: [96m0.9908[0m (433/437)
   • 中间层2准确率: [96m0.9931[0m (434/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 40 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6500[0m (13/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.6578[0m | [1mUAR:[0m [1;96m0.6648[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (5/300) 剩余耐心: 295[0m

[1m[1;96m📅 Epoch 41/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 41 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9908[0m (433/437)
   • 中间层1准确率: [96m0.9908[0m (433/437)
   • 中间层2准确率: [96m0.9817[0m (429/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 41 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7500[0m (15/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7738[0m | [1mUAR:[0m [1;96m0.7418[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (6/300) 剩余耐心: 294[0m

[1m[1;96m📅 Epoch 42/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 42 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9977[0m (436/437)
   • 中间层1准确率: [96m0.9931[0m (434/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 42 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3000[0m (6/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.3833[0m | [1mUAR:[0m [1;96m0.3626[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (7/300) 剩余耐心: 293[0m

[1m[1;96m📅 Epoch 43/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 43 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m0.9954[0m (435/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 43 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8500[0m (17/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.8462[0m | [1mUAR:[0m [1;96m0.8187[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (8/300) 剩余耐心: 292[0m

[1m[1;96m📅 Epoch 44/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 44 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9954[0m (435/437)
   • 中间层1准确率: [96m0.9954[0m (435/437)
   • 中间层2准确率: [96m0.9977[0m (436/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 44 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8000[0m (16/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7971[0m | [1mUAR:[0m [1;96m0.7802[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (9/300) 剩余耐心: 291[0m

[1m[1;96m📅 Epoch 45/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 45 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9863[0m (431/437)
   • 中间层1准确率: [96m0.9474[0m (414/437)
   • 中间层2准确率: [96m0.9840[0m (430/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 45 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6500[0m (13/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.3939[0m | [1mUAR:[0m [1;96m0.5000[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (10/300) 剩余耐心: 290[0m

[1m[1;96m📅 Epoch 46/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 46 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9634[0m (421/437)
   • 中间层1准确率: [96m0.9634[0m (421/437)
   • 中间层2准确率: [96m0.9657[0m (422/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 46 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6500[0m (13/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.6842[0m | [1mUAR:[0m [1;96m0.7308[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (11/300) 剩余耐心: 289[0m

[1m[1;96m📅 Epoch 47/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 47 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9336[0m (408/437)
   • 中间层1准确率: [96m0.9817[0m (429/437)
   • 中间层2准确率: [96m0.9451[0m (413/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 47 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.3000[0m (6/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.4875[0m | [1mUAR:[0m [1;96m0.3297[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (12/300) 剩余耐心: 288[0m

[1m[1;96m📅 Epoch 48/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 48 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9497[0m (415/437)
   • 中间层1准确率: [96m0.9680[0m (423/437)
   • 中间层2准确率: [96m0.9680[0m (423/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 48 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7321[0m | [1mUAR:[0m [1;96m0.7033[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (13/300) 剩余耐心: 287[0m

[1m[1;96m📅 Epoch 49/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 49 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9863[0m (431/437)
   • 中间层1准确率: [96m0.9817[0m (429/437)
   • 中间层2准确率: [96m0.9886[0m (432/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 49 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7246[0m | [1mUAR:[0m [1;96m0.7033[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (14/300) 剩余耐心: 286[0m

[1m[1;96m📅 Epoch 50/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 50 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9634[0m (421/437)
   • 中间层1准确率: [96m0.9588[0m (419/437)
   • 中间层2准确率: [96m0.9657[0m (422/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 50 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.9000[0m (18/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.8810[0m | [1mUAR:[0m [1;96m0.8571[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (15/300) 剩余耐心: 285[0m

[1m[1;96m📅 Epoch 51/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 51 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9771[0m (427/437)
   • 中间层1准确率: [96m0.9771[0m (427/437)
   • 中间层2准确率: [96m0.9771[0m (427/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 51 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.5000[0m (10/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.5965[0m | [1mUAR:[0m [1;96m0.5495[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (16/300) 剩余耐心: 284[0m

[1m[1;96m📅 Epoch 52/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 52 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9657[0m (422/437)
   • 中间层1准确率: [96m0.9725[0m (425/437)
   • 中间层2准确率: [96m0.9703[0m (424/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 52 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.5312[0m | [1mUAR:[0m [1;96m0.5714[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (17/300) 剩余耐心: 283[0m

[1m[1;96m📅 Epoch 53/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 53 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9771[0m (427/437)
   • 中间层1准确率: [96m0.9657[0m (422/437)
   • 中间层2准确率: [96m0.9748[0m (426/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 53 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.6000[0m (12/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.4286[0m | [1mUAR:[0m [1;96m0.4615[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (18/300) 剩余耐心: 282[0m

[1m[1;96m📅 Epoch 54/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 54 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9634[0m (421/437)
   • 中间层1准确率: [96m0.9771[0m (427/437)
   • 中间层2准确率: [96m0.9634[0m (421/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 54 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.2500[0m (5/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.3791[0m | [1mUAR:[0m [1;96m0.3242[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (19/300) 剩余耐心: 281[0m

[1m[1;96m📅 Epoch 55/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 55 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9840[0m (430/437)
   • 中间层1准确率: [96m0.9794[0m (428/437)
   • 中间层2准确率: [96m0.9863[0m (431/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 55 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7500[0m (15/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7442[0m | [1mUAR:[0m [1;96m0.7747[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (20/300) 剩余耐心: 280[0m

[1m[1;96m📅 Epoch 56/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 56 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9908[0m (433/437)
   • 中间层1准确率: [96m0.9931[0m (434/437)
   • 中间层2准确率: [96m0.9863[0m (431/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 56 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7500[0m (15/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7500[0m | [1mUAR:[0m [1;96m0.7418[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (21/300) 剩余耐心: 279[0m

[1m[1;96m📅 Epoch 57/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 57 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9908[0m (433/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m0.9908[0m (433/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 57 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7246[0m | [1mUAR:[0m [1;96m0.7033[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (22/300) 剩余耐心: 278[0m

[1m[1;96m📅 Epoch 58/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 58 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 58 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7500[0m (15/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7013[0m | [1mUAR:[0m [1;96m0.6758[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (23/300) 剩余耐心: 277[0m

[1m[1;96m📅 Epoch 59/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 59 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9931[0m (434/437)
   • 中间层1准确率: [96m0.9931[0m (434/437)
   • 中间层2准确率: [96m0.9977[0m (436/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 59 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7500[0m (15/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7308[0m | [1mUAR:[0m [1;96m0.7088[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (24/300) 剩余耐心: 276[0m

[1m[1;96m📅 Epoch 60/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 60 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 60 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7500[0m (15/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7333[0m | [1mUAR:[0m [1;96m0.7418[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (25/300) 剩余耐心: 275[0m

[1m[1;96m📅 Epoch 61/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 61 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 61 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8000[0m (16/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7619[0m | [1mUAR:[0m [1;96m0.7473[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (26/300) 剩余耐心: 274[0m

[1m[1;96m📅 Epoch 62/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 62 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 62 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8500[0m (17/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.8462[0m | [1mUAR:[0m [1;96m0.8187[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (27/300) 剩余耐心: 273[0m

[1m[1;96m📅 Epoch 63/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 63 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 63 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7038[0m | [1mUAR:[0m [1;96m0.7033[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (28/300) 剩余耐心: 272[0m

[1m[1;96m📅 Epoch 64/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 64 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 64 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7500[0m (15/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7308[0m | [1mUAR:[0m [1;96m0.7088[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (29/300) 剩余耐心: 271[0m

[1m[1;96m📅 Epoch 65/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 65 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 65 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7500[0m (15/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7308[0m | [1mUAR:[0m [1;96m0.7088[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (30/300) 剩余耐心: 270[0m

[1m[1;96m📅 Epoch 66/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 66 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 66 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7500[0m (15/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7308[0m | [1mUAR:[0m [1;96m0.7088[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (31/300) 剩余耐心: 269[0m

[1m[1;96m📅 Epoch 67/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 67 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m1.0000[0m (437/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 67 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8000[0m (16/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7778[0m | [1mUAR:[0m [1;96m0.7473[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (32/300) 剩余耐心: 268[0m

[1m[1;96m📅 Epoch 68/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 68 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m0.9977[0m (436/437)
   • 中间层2准确率: [96m0.9977[0m (436/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 68 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8000[0m (16/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7778[0m | [1mUAR:[0m [1;96m0.7473[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (33/300) 剩余耐心: 267[0m

[1m[1;96m📅 Epoch 69/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 69 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m1.0000[0m (437/437)
   • 中间层1准确率: [96m0.9931[0m (434/437)
   • 中间层2准确率: [96m1.0000[0m (437/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 69 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.8000[0m (16/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.7917[0m | [1mUAR:[0m [1;96m0.8132[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (34/300) 剩余耐心: 266[0m

[1m[1;96m📅 Epoch 70/1429[0m
[1;96m-----------------[0m

============================================================
[1m[1;95m📈 📊 Epoch 70 训练统计 - 011[0m
============================================================
🎯 [1m[1;96m准确率统计:[0m
   • 主网络准确率: [1;92m0.9817[0m (429/437)
   • 中间层1准确率: [96m0.9954[0m (435/437)
   • 中间层2准确率: [96m0.9771[0m (427/437)

💾 [1mGPU内存使用:[0m [1;94mGPU0: 0.3GB/79.3GB (0.3%)[0m
============================================================


🔍 [1m[1;96m开始验证...[0m

============================================================
[1m[1;95m📊 🔍 Epoch 70 验证统计 - 011[0m
============================================================
🎯 [1m验证准确率:[0m [1;96m0.7000[0m (14/20)
🏆 [1m最佳准确率:[0m [1;93m0.9000[0m
📊 [1mUF1:[0m [1;96m0.6970[0m | [1mUAR:[0m [1;96m0.7363[0m
🥇 [1m最佳UF1:[0m [1;93m0.8981[0m | [1m最佳UAR:[0m [1;93m0.8571[0m
============================================================

[1;96m🔄 ⏳ 无改善 (35/300) 剩余耐心: 265[0m

[1m[1;96m📅 Epoch 71/1429[0m
[1;96m-----------------[0m
