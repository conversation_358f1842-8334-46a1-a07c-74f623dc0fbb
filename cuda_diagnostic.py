#!/usr/bin/env python3
"""
CUDA诊断脚本 - 检查CUDA环境和PyTorch配置
用于诊断和解决CUDA初始化错误
"""

import torch
import os
import sys
import subprocess

def print_section(title, symbol="="):
    print(f"\n{symbol * 50}")
    print(f" {title}")
    print(f"{symbol * 50}")

def check_cuda_environment():
    """检查CUDA环境"""
    print_section("CUDA环境检查", "🔍")
    
    # 检查CUDA是否可用
    cuda_available = torch.cuda.is_available()
    print(f"PyTorch CUDA可用性: {cuda_available}")
    
    if not cuda_available:
        print("❌ CUDA不可用，可能的原因：")
        print("   1. 没有安装CUDA驱动")
        print("   2. PyTorch版本与CUDA版本不匹配")
        print("   3. 环境变量配置错误")
        return False
    
    # 获取CUDA版本信息
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA版本: {torch.version.cuda}")
    print(f"cuDNN版本: {torch.backends.cudnn.version()}")
    
    # 获取GPU信息
    gpu_count = torch.cuda.device_count()
    print(f"可用GPU数量: {gpu_count}")
    
    for i in range(gpu_count):
        gpu_name = torch.cuda.get_device_name(i)
        gpu_props = torch.cuda.get_device_properties(i)
        gpu_memory = gpu_props.total_memory / (1024**3)
        print(f"GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
    
    return True

def check_environment_variables():
    """检查环境变量"""
    print_section("环境变量检查", "🌍")
    
    important_vars = [
        'CUDA_VISIBLE_DEVICES',
        'CUDA_DEVICE_ORDER',
        'CUDA_LAUNCH_BLOCKING',
        'TORCH_USE_CUDA_DSA'
    ]
    
    for var in important_vars:
        value = os.environ.get(var, "未设置")
        print(f"{var}: {value}")

def test_basic_cuda_operations():
    """测试基本CUDA操作"""
    print_section("基本CUDA操作测试", "🧪")
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，跳过测试")
        return False
    
    try:
        # 测试张量创建和移动
        print("测试1: 创建CPU张量...")
        cpu_tensor = torch.randn(10, 10)
        print("✅ CPU张量创建成功")
        
        print("测试2: 移动张量到GPU...")
        gpu_tensor = cpu_tensor.cuda()
        print("✅ 张量移动到GPU成功")
        
        print("测试3: GPU上的基本运算...")
        result = gpu_tensor @ gpu_tensor.T
        print("✅ GPU运算成功")
        
        print("测试4: 移动结果回CPU...")
        cpu_result = result.cpu()
        print("✅ 张量移动回CPU成功")
        
        return True
        
    except Exception as e:
        print(f"❌ CUDA操作测试失败: {str(e)}")
        return False

def test_dataloader_cuda():
    """测试DataLoader与CUDA的兼容性"""
    print_section("DataLoader CUDA兼容性测试", "📊")
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，跳过测试")
        return False
    
    try:
        from torch.utils.data import TensorDataset, DataLoader
        
        # 创建测试数据
        print("创建测试数据...")
        X = torch.randn(100, 3, 32, 32)
        y = torch.randint(0, 10, (100,))
        
        # 测试1: CPU数据 + 多进程DataLoader
        print("测试1: CPU数据 + 多进程DataLoader...")
        dataset_cpu = TensorDataset(X, y)
        dataloader_cpu = DataLoader(dataset_cpu, batch_size=16, num_workers=2, pin_memory=True)
        
        for batch_idx, (data, target) in enumerate(dataloader_cpu):
            data = data.cuda()
            target = target.cuda()
            if batch_idx >= 2:  # 只测试几个批次
                break
        print("✅ CPU数据 + 多进程DataLoader 测试成功")
        
        # 测试2: GPU数据 + 单进程DataLoader
        print("测试2: GPU数据 + 单进程DataLoader...")
        X_gpu = X.cuda()
        y_gpu = y.cuda()
        dataset_gpu = TensorDataset(X_gpu, y_gpu)
        dataloader_gpu = DataLoader(dataset_gpu, batch_size=16, num_workers=0, pin_memory=False)
        
        for batch_idx, (data, target) in enumerate(dataloader_gpu):
            if batch_idx >= 2:  # 只测试几个批次
                break
        print("✅ GPU数据 + 单进程DataLoader 测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ DataLoader CUDA测试失败: {str(e)}")
        print("建议解决方案:")
        print("1. 使用 num_workers=0 避免多进程CUDA初始化问题")
        print("2. 将GPU数据移到CPU再创建DataLoader")
        print("3. 设置 CUDA_LAUNCH_BLOCKING=1 获取更详细的错误信息")
        return False

def suggest_fixes():
    """建议修复方案"""
    print_section("修复建议", "💡")
    
    print("如果遇到CUDA初始化错误，请尝试以下解决方案：")
    print()
    print("1. 环境变量设置:")
    print("   export CUDA_LAUNCH_BLOCKING=1")
    print("   export CUDA_DEVICE_ORDER=PCI_BUS_ID")
    print()
    print("2. DataLoader配置:")
    print("   - 当数据在GPU上时，使用 num_workers=0")
    print("   - 当数据在GPU上时，使用 pin_memory=False")
    print("   - 将GPU数据移到CPU再创建DataLoader")
    print()
    print("3. 代码修改示例:")
    print("   # 错误的做法")
    print("   X_gpu = X.cuda()")
    print("   dataset = TensorDataset(X_gpu, y_gpu)")
    print("   dataloader = DataLoader(dataset, num_workers=8, pin_memory=True)")
    print()
    print("   # 正确的做法")
    print("   X_cpu = X.cpu()  # 确保数据在CPU上")
    print("   dataset = TensorDataset(X_cpu, y_cpu)")
    print("   dataloader = DataLoader(dataset, num_workers=0, pin_memory=False)")
    print()
    print("4. 重启Python进程:")
    print("   有时CUDA状态会被破坏，需要重启Python进程")

def main():
    """主函数"""
    print("🚀 CUDA诊断工具启动")
    print("=" * 60)
    
    # 检查CUDA环境
    cuda_ok = check_cuda_environment()
    
    # 检查环境变量
    check_environment_variables()
    
    # 测试基本CUDA操作
    if cuda_ok:
        basic_ok = test_basic_cuda_operations()
        
        # 测试DataLoader
        if basic_ok:
            test_dataloader_cuda()
    
    # 提供修复建议
    suggest_fixes()
    
    print("\n" + "=" * 60)
    print("🏁 诊断完成")

if __name__ == "__main__":
    main()
