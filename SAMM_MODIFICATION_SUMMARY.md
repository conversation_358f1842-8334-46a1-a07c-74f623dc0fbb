# SAMM数据集代码修改总结

## 概述
已成功将原本针对CASME2数据集的代码修改为适用于SAMM数据集的版本。主要修改了以下两个文件：
- `train_classify_SKD_TSTSAN_functions_SAMM2.py` (训练函数库)
- `train_classify_SKD_TSTSANSAMM2.py` (主训练脚本)

## 主要修改内容

### 1. 数据集配置修改

#### 样本数量统计
```python
# 原CASME2配置
CASME2_5class_numbers = [32, 25, 63, 27, 99]  # 5分类
CASME2_3class_numbers = [32, 90, 25]  # 3分类

# 修改为SAMM配置
SAMM_3class_numbers = [26, 90, 15]  # 3分类 (positive, negative, surprise)
SAMM_SAMPLE_STATS = {
    'positive': 26,   # 正面情绪
    'negative': 90,   # 负面情绪
    'surprise': 15,   # 惊讶情绪
    'total': 131      # 总样本数
}
```

#### 受试者分组
```python
# SAMM数据集3分类的分组（基于测试样本数）
LARGE_SUBJECTS_SAMM = ['011', '006', '014', '026']  # 大样本组(>8个测试样本)
MEDIUM_SUBJECTS_SAMM = ['007', '035', '013', '016', '022', '033']  # 中等样本组(5-8个测试样本)
SMALL_SUBJECTS_SAMM = ['009', '010', '017', '020', '012', '015', '018', '030', '032', '034', '021', '028']  # 小样本组(3-4个测试样本)
VERY_SMALL_SUBJECTS_SAMM = ['019', '023', '024', '031', '036', '037']  # 极小样本组(1-2个测试样本)

# 训练顺序（按测试样本数量从多到少）
TRAINING_ORDER_SAMM = [
    '011', '006', '014', '026',  # 大样本组
    '007', '035', '013', '016', '022', '033',  # 中等样本组
    '009', '010', '017', '020', '012', '015', '018', '030', '032', '034', '021', '028',  # 小样本组
    '019', '023', '024', '031', '036', '037'  # 极小样本组
]
```

### 2. 标签处理修改

#### 移除5分类到3分类的转换逻辑
```python
# 原CASME2代码需要转换
if config.class_num == 3:
    converted_label = convert_5class_to_3class(original_label)
    if converted_label is None:  # 过滤掉"其他"类别
        continue
    label = converted_label

# 修改为SAMM直接使用3分类
label = int(n_expression)  # SAMM数据集直接使用3分类标签，不需要转换
```

#### 情绪标签映射
```python
# SAMM数据集使用3分类标签字典
label_dict = {'positive': 0, 'negative': 1, 'surprise': 2}
emotion_names = ['正性(positive)', '负性(negative)', '惊讶(surprise)']
```

### 3. 参数配置修改

#### 默认路径和参数
```python
# 数据集路径
parser.add_argument('--main_path', type=str, 
                   default="/home/<USER>/data/ajq/SKD-TSTSAN-data/SAMM_LOSO_full", 
                   help="数据集主目录路径")

# 预训练模型路径
parser.add_argument('--pre_trained_model_path', type=str, 
                   default="/home/<USER>/data/ajq/SKD-TSTSAN-new/Pretrained_model/ck注意力融合_class7_pretrain_conv3_bior2.2_L2_eca1_relu_attn_h8.pth", 
                   help="宏表情数据集预训练模型权重的路径")

# 数据增强倍数
parser.add_argument('--aug_multipliers', type=str, default='7,2,10', 
                   help='训练数据各标签扩充倍数(逗号分隔) - SAMM 3分类: positive,negative,surprise')
parser.add_argument('--test_aug_multipliers', type=str, default='7,2,10', 
                   help='测试数据各标签扩充倍数(逗号分隔) - SAMM 3分类: positive,negative,surprise')

# 测试镜像训练受试者
parser.add_argument('--test_mirror_subjects', type=str, default='017,014,007,020,006',
                   help='需要使用测试数据镜像训练的受试者列表(逗号分隔,如017,014)')

# 数据集名称
parser.add_argument('--dataset', type=str, default="SAMM", help='数据集名称')

# 分类数量
parser.add_argument('--class_num', type=int, default=3, help='分类类别数(SAMM数据集固定为3分类)')
```

### 4. 用户界面修改

#### 移除分类方案选择
```python
# 原CASME2代码允许选择3分类或5分类
# 修改为SAMM固定3分类
print_section("数据集配置", Colors.BRIGHT_GREEN, "🎯")
print_success("SAMM数据集使用3分类方案 (positive, negative, surprise)", "🎯")
parser.set_defaults(class_num=3)
parser.set_defaults(aug_multipliers="7,2,10")
parser.set_defaults(test_aug_multipliers="7,2,10")
```

### 5. 报告和日志修改

#### 邮件报告标题
```python
# 修改邮件主题和内容
subject_line = f'[SAMM {class_scheme}实验总结] {config.exp_name} - UF1={uf1_value}, UAR={uar_value}'
```

#### 实验报告内容
```python
# 添加SAMM数据集特定信息
content = f"""
【🎯 SAMM微表情识别实验报告 - {class_scheme}】
数据集: SAMM (Spontaneous Micro-expression Database)
SAMM数据集包含131个微表情样本，分为3个类别：
- positive (正性): 26个样本
- negative (负性): 90个样本  
- surprise (惊讶): 15个样本
"""
```

### 6. 函数参数修改

#### 默认参数调整
```python
# 所有相关函数的class_num默认值改为3
def get_subject_parameters(subject_id, class_num=3):
def get_subject_temperature(subject_id, class_num=3):
def get_subject_alpha(subject_id, current_iter, max_iter, class_num=3):
def get_subject_beta(subject_id, class_num=3):
def new_kd_loss_function(output, target_output, current_iter, max_iter, subject_id, class_num=3):
def feature_loss_function(fea, target_fea, current_iter, max_iter, subject_id, class_num=3):
```

## 使用说明

### 运行命令示例
```bash
python train_classify_SKD_TSTSANSAMM2.py
```

### 主要特点
1. **固定3分类**: SAMM数据集只支持3分类 (positive, negative, surprise)
2. **自动配置**: 无需手动选择分类方案，自动使用SAMM的3分类配置
3. **优化的数据增强**: 根据SAMM数据集的类别分布调整了增强倍数
4. **受试者特定优化**: 根据SAMM数据集的受试者样本分布进行了分组优化
5. **完整的实验报告**: 包含SAMM数据集特定的统计信息和分析

### 注意事项
1. 确保数据集路径正确指向SAMM数据集
2. 预训练模型路径需要指向合适的预训练权重
3. 受试者ID格式为3位数字字符串 (如 '011', '006' 等)
4. 数据增强倍数已根据SAMM数据集的类别不平衡情况进行了优化

## 文件结构
```
/home/<USER>/data/ajq/SKD-TSTSAN-new/
├── train_classify_SKD_TSTSAN_functions_SAMM2.py  # 训练函数库
├── train_classify_SKD_TSTSANSAMM2.py             # 主训练脚本
└── SAMM_MODIFICATION_SUMMARY.md                  # 本修改总结文档
```
